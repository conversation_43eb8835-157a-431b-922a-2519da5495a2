#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据融合脚本 - 将处理后的波形数据融合到原始Excel文件中
功能：
1. 读取文件名_waveform_processed.xlsx中的数据
2. 根据工作表名称定义剖面名
3. 在对应的原始文件名.xlsx中找到相应剖面
4. 将比值数据（Column E）根据距离排序后填入原始文件的energy列
5. 在PSD数据栏后增加energy column
"""

import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path

def find_psd_column_position(df):
    """
    查找PSD数据栏的位置
    
    Args:
        df: pandas DataFrame
    
    Returns:
        int: PSD列的最后位置，如果没找到返回最后一列的位置
    """
    # 查找包含"PSD"的列
    psd_columns = [col for col in df.columns if 'PSD' in str(col).upper()]
    
    if psd_columns:
        # 找到最后一个PSD列的位置
        last_psd_col = psd_columns[-1]
        last_psd_index = df.columns.get_loc(last_psd_col)
        print(f"    找到PSD列，最后一个PSD列: {last_psd_col}，位置: {last_psd_index}")
        return last_psd_index + 1  # 返回PSD列后的位置
    else:
        # 如果没有找到PSD列，在最后添加
        print(f"    未找到PSD列，将在最后添加energy列")
        return len(df.columns)

def process_single_file_pair(processed_file, original_file, output_dir):
    """
    处理单对文件（processed和original）

    Args:
        processed_file: 处理后的文件路径（*_waveform_processed.xlsx）
        original_file: 原始文件路径（*.xlsx）
        output_dir: 输出目录

    Returns:
        bool: 处理是否成功
    """
    print(f"\n处理文件对:")
    print(f"  处理后文件: {os.path.basename(processed_file)}")
    print(f"  原始文件: {os.path.basename(original_file)}")

    try:
        # 读取处理后的文件
        processed_excel = pd.ExcelFile(processed_file)
        processed_sheets = processed_excel.sheet_names
        print(f"  处理后文件包含剖面: {processed_sheets}")

        # 读取原始文件
        original_excel = pd.ExcelFile(original_file)
        original_sheets = original_excel.sheet_names
        print(f"  原始文件包含工作表: {original_sheets}")

        # 生成输出文件名
        original_name = os.path.splitext(os.path.basename(original_file))[0]
        output_filename = f"{original_name}_with_energy.xlsx"
        output_path = os.path.join(output_dir, output_filename)

        # 创建Excel写入器
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            processed_count = 0

            # 处理原始文件的每个工作表
            for sheet_name in original_sheets:
                print(f"\n  处理原始工作表: {sheet_name}")

                # 读取原始工作表数据
                original_df = pd.read_excel(original_file, sheet_name=sheet_name)

                if original_df.empty:
                    print(f"    警告: 原始工作表 {sheet_name} 为空")
                    original_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    continue

                result_df = original_df.copy()

                # 如果是数据表工作表，需要为每个剖面添加energy列
                if sheet_name == "数据表":
                    print(f"    检测到数据表工作表，开始处理剖面数据")
                    print(f"    原始数据列名: {list(original_df.columns)}")

                    # 查找剖面列（如2-3, 1-3, 1-2等）
                    profile_columns = []
                    for col in original_df.columns:
                        col_str = str(col)
                        # 检查是否是剖面名称格式（数字-数字）
                        if '-' in col_str and len(col_str.split('-')) == 2:
                            parts = col_str.split('-')
                            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                                profile_columns.append(col)

                    print(f"    找到剖面列: {profile_columns}")

                    # 为每个剖面添加energy列
                    for profile_name in profile_columns:
                        if profile_name in processed_sheets:
                            print(f"    处理剖面: {profile_name}")

                            # 读取对应的处理后数据
                            processed_df = pd.read_excel(processed_file, sheet_name=profile_name)

                            if not processed_df.empty:
                                # 查找距离列和比值列
                                distance_col = None
                                ratio_col = None

                                for col in processed_df.columns:
                                    if '距离' in str(col):
                                        distance_col = col
                                    elif '比值' in str(col):
                                        ratio_col = col

                                if distance_col is not None and ratio_col is not None:
                                    # 按距离排序处理后的数据
                                    processed_df_sorted = processed_df.sort_values(distance_col).reset_index(drop=True)

                                    print(f"      处理后数据行数: {len(processed_df_sorted)}")
                                    print(f"      距离范围: {processed_df_sorted[distance_col].min():.1f} - {processed_df_sorted[distance_col].max():.1f}")
                                    print(f"      比值范围: {processed_df_sorted[ratio_col].min():.6f} - {processed_df_sorted[ratio_col].max():.6f}")

                                    # 创建energy列名
                                    energy_col_name = f"{profile_name}_energy"

                                    # 查找原始数据中的深度列
                                    depth_col = None
                                    for col in result_df.columns:
                                        if '深度' in str(col) or 'depth' in str(col).lower() or col == '深度':
                                            depth_col = col
                                            break

                                    # 如果没有找到深度列，尝试查找第一列（通常是深度）
                                    if depth_col is None and len(result_df.columns) > 0:
                                        # 检查第一列是否包含数值数据
                                        first_col = result_df.columns[0]
                                        if result_df[first_col].dtype in ['float64', 'int64'] or pd.to_numeric(result_df[first_col], errors='coerce').notna().any():
                                            depth_col = first_col
                                            print(f"      使用第一列作为深度列: {depth_col}")

                                    if depth_col is not None:
                                        print(f"      找到深度列: {depth_col}")

                                        # 创建energy列数据，按行号顺序匹配（不再按深度匹配）
                                        energy_data = []

                                        # 获取数据起始行（跳过标题行）
                                        data_start_row = 9  # 从第10行开始是实际数据

                                        for i in range(len(result_df)):
                                            # 检查是否是标题行或特殊行，如果是则添加"energy%"标识
                                            original_depth = result_df.iloc[i][depth_col]

                                            # 检查是否是标题行（包含"深度"、"(m)"等标识）
                                            if pd.isna(original_depth) or str(original_depth).strip() == '' or '深度' in str(original_depth) or '(m)' in str(original_depth) or 'depth' in str(original_depth).lower():
                                                energy_data.append('energy%')
                                                continue

                                            # 检查是否是数据行
                                            if i >= data_start_row:
                                                # 计算在处理后数据中的对应行号
                                                processed_row_idx = i - data_start_row

                                                # 检查是否在处理后数据范围内
                                                if processed_row_idx < len(processed_df_sorted):
                                                    energy_value = processed_df_sorted.iloc[processed_row_idx][ratio_col]
                                                    energy_data.append(energy_value)
                                                    print(f"        行 {i+1}: 深度 {original_depth} -> energy {energy_value}")
                                                else:
                                                    energy_data.append(np.nan)
                                                    print(f"        行 {i+1}: 深度 {original_depth} -> 超出范围，设为nan")
                                            else:
                                                # 非数据行，设为nan
                                                energy_data.append(np.nan)

                                        # 找到剖面列的位置，在其后插入energy列
                                        profile_col_index = result_df.columns.get_loc(profile_name)
                                        result_df.insert(profile_col_index + 1, energy_col_name, energy_data)

                                        # 统计有效数据
                                        valid_count = sum(1 for x in energy_data if not pd.isna(x))
                                        print(f"      已在剖面 {profile_name} 后插入 {energy_col_name} 列")
                                        print(f"      成功匹配 {valid_count}/{len(energy_data)} 个深度点")
                                        processed_count += 1
                                    else:
                                        print(f"      警告: 未找到深度列，无法进行深度匹配")
                                else:
                                    print(f"      警告: 剖面 {profile_name} 的处理后数据中未找到距离列或比值列")
                            else:
                                print(f"      警告: 剖面 {profile_name} 的处理后数据为空")
                        else:
                            print(f"      警告: 未找到剖面 {profile_name} 的处理后数据")

                # 确保工作表名称符合Excel规范
                safe_sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace(':', '_').replace('[', '_').replace(']', '_')
                if len(safe_sheet_name) > 31:
                    safe_sheet_name = safe_sheet_name[:31]

                # 写入工作表
                result_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)

        print(f"\n  已保存融合后的文件: {output_path}")
        print(f"  成功处理 {processed_count} 个剖面的数据融合")
        return True

    except Exception as e:
        print(f"  处理文件对时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_file_pairs(input_dir):
    """
    查找文件对（processed和original）
    
    Args:
        input_dir: 输入目录
    
    Returns:
        list: 文件对列表 [(processed_file, original_file), ...]
    """
    # 查找所有processed文件
    processed_files = glob.glob(os.path.join(input_dir, "*_waveform_processed.xlsx"))
    
    file_pairs = []
    
    for processed_file in processed_files:
        # 从processed文件名推导原始文件名
        processed_name = os.path.basename(processed_file)
        # 移除"_waveform_processed.xlsx"后缀
        original_name = processed_name.replace("_waveform_processed.xlsx", ".xlsx")
        original_file = os.path.join(input_dir, original_name)
        
        if os.path.exists(original_file):
            file_pairs.append((processed_file, original_file))
            print(f"找到文件对: {processed_name} <-> {original_name}")
        else:
            print(f"警告: 未找到对应的原始文件: {original_name}")
    
    return file_pairs

def main():
    """
    主函数 - 批量处理数据融合
    """
    # 设置输入和输出目录为脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_dir = script_dir
    output_dir = script_dir  # 输出到脚本所在目录
    
    print("开始数据融合处理...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 查找文件对
    file_pairs = find_file_pairs(input_dir)
    
    if not file_pairs:
        print("未找到任何文件对")
        return
    
    print(f"\n找到 {len(file_pairs)} 个文件对")
    
    # 处理每个文件对
    success_count = 0
    total_count = len(file_pairs)
    
    for processed_file, original_file in file_pairs:
        if process_single_file_pair(processed_file, original_file, output_dir):
            success_count += 1
    
    print(f"\n数据融合处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件对")
    print(f"融合后的文件已保存到: {output_dir}")
    print("文件名格式为: 原文件名_with_energy.xlsx")

if __name__ == "__main__":
    main()
