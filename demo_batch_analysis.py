#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量分析功能演示脚本
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def demo_batch_analysis():
    """演示批量分析功能"""
    
    # 检查是否存在测试文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_files = [f for f in os.listdir(current_dir) if f.endswith('_with_energy.txt')]
    
    if not test_files:
        print("未找到测试文件，创建演示说明...")
        show_demo_info()
        return
    
    print(f"找到 {len(test_files)} 个测试文件:")
    for i, filename in enumerate(test_files, 1):
        print(f"  {i}. {filename}")
    
    print("\n启动批量分析GUI...")
    
    try:
        # 导入并启动GUI
        from gz_analysis_gui import GZAnalysisGUI
        
        root = tk.Tk()
        app = GZAnalysisGUI(root)
        
        # 自动设置文件夹路径
        app.folder_path_var.set(current_dir)
        app.current_folder = current_dir
        
        # 自动扫描文件
        app.scan_files()
        
        print("GUI已启动，请按照以下步骤操作:")
        print("1. 文件夹已自动设置为当前目录")
        print("2. 文件列表已自动加载")
        print("3. 点击'加载选中文件'")
        print("4. 配置分析参数（可选）")
        print("5. 点击'开始批量分析'")
        print("6. 等待分析完成")
        print("7. 查看结果并保存")
        
        root.mainloop()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 gz_traditional_analysis.py 文件存在")
    except Exception as e:
        print(f"启动错误: {e}")

def show_demo_info():
    """显示演示信息"""
    
    info = """
    批量分析功能演示
    ================
    
    当前目录中没有找到 *_with_energy.txt 文件。
    
    要演示批量分析功能，请：
    
    1. 将多个 *_with_energy.txt 文件放入当前目录
    2. 或者运行 python gz_analysis_gui.py
    3. 选择包含这些文件的文件夹
    
    文件格式要求：
    - 文件名必须以 _with_energy.txt 结尾
    - 文件内容应该是标准的桩基检测数据格式
    - 包含深度、速度、幅度、能量等列
    
    示例文件名：
    - KBZ1-1_with_energy.txt
    - KBZ1-2_with_energy.txt
    - Pile_A_with_energy.txt
    - Test_Data_with_energy.txt
    
    批量分析功能特点：
    ✓ 自动扫描文件夹中的所有符合格式的文件
    ✓ 支持多文件选择和批量处理
    ✓ 实时显示分析进度
    ✓ 汇总所有文件的分析结果
    ✓ 支持多种格式保存结果
    """
    
    print(info)
    
    # 如果有tkinter，显示图形界面信息
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        messagebox.showinfo("批量分析演示", 
                           "当前目录中没有找到 *_with_energy.txt 文件。\n\n"
                           "请将测试文件放入当前目录，或者\n"
                           "直接运行 python gz_analysis_gui.py\n"
                           "选择包含测试文件的文件夹。")
        
        root.destroy()
    except:
        pass

def main():
    """主函数"""
    print("=" * 60)
    print("GZ传统分析 - 批量分析功能演示")
    print("=" * 60)
    
    demo_batch_analysis()

if __name__ == "__main__":
    main()