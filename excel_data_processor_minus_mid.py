#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理脚本 - 批量处理文件夹中的所有XLSX文件
对每个XLSX文件的所有工作表进行数据处理：
- Column A: 测点序号（保留）
- Column B: 距离（保留）
- Column C: 每行数据减去中位数后的绝对值之和
  计算方法：
  1. 计算Column C到Column L数据的中位数
  2. 每行除了Column A和B之外的所有数据减去该中位数
  3. 取绝对值后求和
- Column D: 从Column C中选取最大的9个值，去掉其中3个最大值后剩余6个值的平均数
- Column E: Column C 除以 Column D
- 最后按Column B（距离）从小到大重新排列所有数据
保存为Excel格式，文件名为原xlsx文件名，包含多个工作表，工作表名称保持不变
"""

import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, simpledialog

class DataRangeDialog:
    def __init__(self):
        self.start_col = 200
        self.end_col = 500
        self.result = None

    def show_dialog(self):
        """显示数据范围选择对话框"""
        root = tk.Tk()
        root.title("数据范围选择")
        root.geometry("450x300")
        root.resizable(False, False)

        # 居中显示窗口
        root.eval('tk::PlaceWindow . center')

        # 创建主框架
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="选择Column C计算的数据范围",
                              font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 15))

        # 说明文字
        desc_label = tk.Label(main_frame,
                             text="请选择从Excel文件的第几列开始到第几列结束\n用于计算Column C的数据总和",
                             font=("Arial", 9))
        desc_label.pack(pady=(0, 15))

        # 开始列输入
        start_frame = tk.Frame(main_frame)
        start_frame.pack(fill=tk.X, pady=8)
        tk.Label(start_frame, text="开始列（包含）:", font=("Arial", 11, "bold")).pack(side=tk.LEFT)
        self.start_entry = tk.Entry(start_frame, width=15, font=("Arial", 12),
                                   relief="solid", bd=2, justify="center")
        self.start_entry.pack(side=tk.RIGHT)
        self.start_entry.insert(0, str(self.start_col))

        # 结束列输入
        end_frame = tk.Frame(main_frame)
        end_frame.pack(fill=tk.X, pady=8)
        tk.Label(end_frame, text="结束列（包含）:", font=("Arial", 11, "bold")).pack(side=tk.LEFT)
        self.end_entry = tk.Entry(end_frame, width=15, font=("Arial", 12),
                                 relief="solid", bd=2, justify="center")
        self.end_entry.pack(side=tk.RIGHT)
        self.end_entry.insert(0, str(self.end_col))

        # 提示信息
        hint_label = tk.Label(main_frame,
                             text="注意：列数从1开始计算，Column A=1, Column B=2...",
                             font=("Arial", 8), fg="gray")
        hint_label.pack(pady=(10, 0))

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))

        # 确定按钮
        ok_button = tk.Button(button_frame, text="确定", command=self.on_ok,
                             font=("Arial", 12, "bold"), width=12, height=2,
                             bg="#4CAF50", fg="white", relief="raised", bd=3)
        ok_button.pack(side=tk.LEFT, padx=(0, 15))

        # 取消按钮
        cancel_button = tk.Button(button_frame, text="取消", command=self.on_cancel,
                                 font=("Arial", 12, "bold"), width=12, height=2,
                                 bg="#f44336", fg="white", relief="raised", bd=3)
        cancel_button.pack(side=tk.LEFT)

        # 绑定回车键
        root.bind('<Return>', lambda e: self.on_ok())
        root.bind('<Escape>', lambda e: self.on_cancel())

        # 设置焦点
        self.start_entry.focus()
        self.start_entry.select_range(0, tk.END)

        self.root = root
        root.mainloop()

        return self.result

    def on_ok(self):
        try:
            start = int(self.start_entry.get())
            end = int(self.end_entry.get())

            if start < 3:  # Column A=1, Column B=2, 所以最小从3开始
                messagebox.showerror("错误", "开始列不能小于3（Column C之后）")
                return

            if end <= start:
                messagebox.showerror("错误", "结束列必须大于开始列")
                return

            self.result = (start, end)
            self.root.destroy()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def on_cancel(self):
        self.result = None
        self.root.destroy()

def get_data_range():
    """获取用户选择的数据范围"""
    dialog = DataRangeDialog()
    result = dialog.show_dialog()

    if result is None:
        print("用户取消了操作")
        return None

    start_col, end_col = result
    print(f"用户选择的数据范围: 第{start_col}列到第{end_col}列")
    return start_col, end_col

def process_single_worksheet(df, sheet_name):
    """
    处理单个工作表的数据

    Args:
        df: pandas DataFrame，工作表数据
        sheet_name: 工作表名称

    Returns:
        processed_df: 处理后的DataFrame
    """
    print(f"    处理工作表: {sheet_name}")

    # 创建结果DataFrame
    result_df = pd.DataFrame()

    # 保留Column A (测点序号) 和 Column B (距离)
    if len(df.columns) >= 2:
        result_df['测点序号'] = df.iloc[:, 0]  # Column A
        result_df['距离'] = df.iloc[:, 1]      # Column B
    else:
        print(f"      警告: 工作表 {sheet_name} 列数不足")
        return pd.DataFrame()

    # 计算Column C: 根据新算法
    if len(df.columns) > 2:
        # 获取所有波形数据（第3列到最后一列）
        waveform_data = df.iloc[:, 2:]
        waveform_data_numeric = pd.to_numeric(waveform_data.stack(), errors='coerce').unstack()
        waveform_data_numeric = waveform_data_numeric.fillna(0)

        # 计算Column C到L数据的中位数（第3列到第12列）
        # 注意：如果总列数不足12，则取实际有的列
        c_to_l_end = min(12, len(df.columns))
        c_to_l_data = df.iloc[:, 2:c_to_l_end]
        c_to_l_numeric = pd.to_numeric(c_to_l_data.stack(), errors='coerce').unstack().fillna(0)

        # 计算Column C到L所有数据的中位数（整体中位数）
        all_c_to_l_values = c_to_l_numeric.values.flatten()
        all_c_to_l_values = all_c_to_l_values[~np.isnan(all_c_to_l_values)]  # 去除NaN
        median_value = np.median(all_c_to_l_values)

        print(f"      Column C到L的数据范围: 第3列到第{c_to_l_end}列")
        print(f"      Column C到L数据的中位数: {median_value:.2f}")

        # Column C: 每行数据减去中位数后的绝对值之和
        # 对每一行的波形数据，减去中位数，取绝对值，然后求和
        result_df['数据总和'] = waveform_data_numeric.sub(median_value).abs().sum(axis=1)

        # 计算Column D: 从Column C中选取最大的9个值，去掉其中3个最大值后剩余6个值的平均数
        if len(result_df) >= 9:
            # 获取Column C中最大的9个值
            top_9_indices = result_df['数据总和'].nlargest(9).index
            top_9_values = result_df.loc[top_9_indices, '数据总和']

            # 去掉其中3个最大值，剩余6个值
            sorted_top_9 = top_9_values.sort_values(ascending=False)
            remaining_6 = sorted_top_9.iloc[3:]  # 去掉前3个最大值

            # 计算这6个值的平均数
            average_of_6 = remaining_6.mean()

            print(f"      Column C最大的9个值: {sorted_top_9.values}")
            print(f"      去掉3个最大值后的6个值: {remaining_6.values}")
            print(f"      这6个值的平均数: {average_of_6:.2f}")

        elif len(result_df) >= 6:
            # 如果数据行数少于9但大于等于6，取所有值的平均数
            average_of_6 = result_df['数据总和'].mean()
            print(f"      数据行数少于9，使用所有值的平均数: {average_of_6:.2f}")
        else:
            # 数据行数太少
            average_of_6 = result_df['数据总和'].mean() if len(result_df) > 0 else 0
            print(f"      数据行数太少，使用所有值的平均数: {average_of_6:.2f}")

        # Column D: 填入计算出的平均数
        result_df['筛选后数据平均'] = average_of_6

        # 计算Column E: Column C 除以 Column D
        result_df['比值'] = np.where(
            result_df['筛选后数据平均'] != 0,
            result_df['数据总和'] / result_df['筛选后数据平均'],
            0
        )
        
    else:
        print(f"      警告: 工作表 {sheet_name} 没有波形数据列")
        result_df['数据总和'] = 0
        result_df['筛选后数据平均'] = 0
        result_df['比值'] = 0

    # 按Column B（距离）从小到大重新排列数据
    if len(result_df) > 0:
        # 确保距离列是数值类型
        result_df['距离'] = pd.to_numeric(result_df['距离'], errors='coerce')
        # 按距离排序
        result_df = result_df.sort_values('距离', ascending=True).reset_index(drop=True)
        print(f"      已按距离从小到大重新排列数据")

    print(f"      处理完成: {len(result_df)} 行数据")
    return result_df

def process_excel_file(excel_file_path, output_dir):
    """
    处理单个Excel文件

    Args:
        excel_file_path: Excel文件路径
        output_dir: 输出目录

    Returns:
        bool: 处理是否成功
    """
    print(f"\n处理Excel文件: {excel_file_path}")

    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(excel_file_path)
        sheet_names = excel_file.sheet_names

        print(f"  找到 {len(sheet_names)} 个工作表: {sheet_names}")

        # 获取文件名（不含扩展名）
        file_name = os.path.splitext(os.path.basename(excel_file_path))[0]

        # 生成输出Excel文件名
        output_filename = f"{file_name}_processed.xlsx"
        output_path = os.path.join(output_dir, output_filename)

        # 创建Excel写入器
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            processed_sheets = 0

            # 处理每个工作表
            for sheet_name in sheet_names:
                print(f"  处理工作表: {sheet_name}")

                # 读取工作表数据
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

                if df.empty:
                    print(f"    警告: 工作表 {sheet_name} 为空")
                    continue

                # 处理数据
                processed_df = process_single_worksheet(df, sheet_name)

                if processed_df.empty:
                    print(f"    警告: 工作表 {sheet_name} 处理后为空")
                    continue

                # 确保工作表名称符合Excel规范（最大31个字符，不能包含特殊字符）
                safe_sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace(':', '_').replace('[', '_').replace(']', '_')
                if len(safe_sheet_name) > 31:
                    safe_sheet_name = safe_sheet_name[:31]

                # 写入工作表
                processed_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
                processed_sheets += 1

                print(f"    工作表 '{safe_sheet_name}': {len(processed_df)} 行数据")

                # 显示统计信息
                if len(processed_df) > 0:
                    print(f"    数据总和范围: {processed_df['数据总和'].min():.2f} - {processed_df['数据总和'].max():.2f}")
                    print(f"    筛选后平均值范围: {processed_df['筛选后数据平均'].min():.2f} - {processed_df['筛选后数据平均'].max():.2f}")
                    print(f"    比值范围: {processed_df['比值'].min():.6f} - {processed_df['比值'].max():.6f}")

        if processed_sheets > 0:
            print(f"  已保存Excel文件: {output_path}")
            print(f"  成功处理 {processed_sheets} 个工作表")
            return True
        else:
            print(f"  警告: 没有成功处理任何工作表")
            return False

    except Exception as e:
        print(f"  处理文件 {excel_file_path} 时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数 - 批量处理文件夹中的所有XLSX文件
    """
    # 设置输入和输出目录为脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_dir = script_dir
    output_dir = script_dir  # 输出到脚本所在目录
    
    print("开始批量处理Excel数据...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 查找所有XLSX文件
    xlsx_files = glob.glob(os.path.join(input_dir, "*.xlsx"))
    
    if not xlsx_files:
        print(f"在目录 {input_dir} 中未找到任何XLSX文件")
        return
    
    print(f"\n找到 {len(xlsx_files)} 个XLSX文件:")
    for xlsx_file in xlsx_files:
        print(f"  - {os.path.basename(xlsx_file)}")

    # 处理每个文件
    success_count = 0
    total_count = len(xlsx_files)

    print("\n开始处理文件...")
    print("Column C计算方法: 每行数据减去Column C到L的中位数后的绝对值之和")

    for xlsx_file in xlsx_files:
        if process_excel_file(xlsx_file, output_dir):
            success_count += 1
    
    print(f"\n批量处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件")
    print(f"Excel文件已保存到: {output_dir}")
    print("每个原始Excel文件生成一个处理后的Excel文件，文件名格式为: 原文件名_processed.xlsx")
    print("处理后的Excel文件包含多个工作表，工作表名称保持不变")

if __name__ == "__main__":
    main()
