#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GZ Traditional Analysis GUI
GZ传统分析图形用户界面

A user-friendly GUI for GZ Traditional Analysis functionality.
基于tkinter的GZ传统分析图形界面。

Author: Pile Integrity Analysis System
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import json
from datetime import datetime
import pandas as pd

# Import the GZ analysis module
try:
    from gz_traditional_analysis import GZTraditionalAnalyzer
except ImportError:
    messagebox.showerror("错误", "无法导入 gz_traditional_analysis 模块。请确保文件在同一目录中。")
    sys.exit(1)

class GZAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GZ Traditional Analysis - 桩基完整性分析")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.analyzer = None
        self.current_file = None
        self.results = None
        
        # Create GUI components
        self.create_widgets()
        self.setup_layout()
        
        # Set default values
        self.set_default_values()
        
    def create_widgets(self):
        """创建GUI组件"""
        
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # File selection frame
        self.file_frame = ttk.LabelFrame(self.main_frame, text="数据文件选择", padding="10")
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.file_path_var, width=60)
        self.browse_button = ttk.Button(self.file_frame, text="浏览...", command=self.browse_file)
        self.load_button = ttk.Button(self.file_frame, text="加载数据", command=self.load_data)
        
        # Configuration frame
        self.config_frame = ttk.LabelFrame(self.main_frame, text="分析配置", padding="10")
        
        # Indicators configuration
        self.indicators_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.indicators_frame, text="启用指标:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.speed_var = tk.BooleanVar(value=True)
        self.amplitude_var = tk.BooleanVar(value=True)
        self.energy_var = tk.BooleanVar(value=True)
        self.psd_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(self.indicators_frame, text="声速", variable=self.speed_var).grid(row=0, column=1, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="波幅", variable=self.amplitude_var).grid(row=0, column=2, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="能量", variable=self.energy_var).grid(row=0, column=3, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="PSD", variable=self.psd_var).grid(row=0, column=4, padx=5)
        
        # Depth range configuration
        self.depth_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.depth_frame, text="深度范围(m):").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.depth_range_var = tk.DoubleVar(value=0.5)
        self.depth_spinbox = ttk.Spinbox(self.depth_frame, from_=0.1, to=2.0, increment=0.1, 
                                        textvariable=self.depth_range_var, width=10)
        self.depth_spinbox.grid(row=0, column=1, padx=5)
        
        self.enable_depth_range_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(self.depth_frame, text="启用深度范围", 
                       variable=self.enable_depth_range_var).grid(row=0, column=2, padx=10)
        
        # Control buttons frame
        self.control_frame = ttk.Frame(self.main_frame)

        self.analyze_button = ttk.Button(self.control_frame, text="开始分析",
                                        command=self.start_analysis, state="disabled")
        self.save_button = ttk.Button(self.control_frame, text="保存结果",
                                     command=self.save_results, state="disabled")
        self.clear_button = ttk.Button(self.control_frame, text="清除结果", command=self.clear_results)
        self.config_button = ttk.Button(self.control_frame, text="加载配置", command=self.load_config)
        self.save_config_button = ttk.Button(self.control_frame, text="保存配置", command=self.save_config)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(self.control_frame, textvariable=self.progress_var)
        self.progress_bar = ttk.Progressbar(self.control_frame, mode='indeterminate')
        
        # Results frame
        self.results_frame = ttk.LabelFrame(self.main_frame, text="分析结果", padding="10")
        
        # Results summary
        self.summary_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.summary_frame, text="桩类判定:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.category_var = tk.StringVar(value="未分析")
        self.category_label = ttk.Label(self.summary_frame, textvariable=self.category_var, 
                                       font=("Arial", 12, "bold"))
        self.category_label.grid(row=0, column=1, sticky="w")
        
        ttk.Label(self.summary_frame, text="分析截面数:").grid(row=0, column=2, sticky="w", padx=(20, 10))
        self.sections_var = tk.StringVar(value="0")
        ttk.Label(self.summary_frame, textvariable=self.sections_var).grid(row=0, column=3, sticky="w")
        
        # K-value distribution
        self.k_dist_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.k_dist_frame, text="K值分布:").grid(row=0, column=0, sticky="nw", padx=(0, 10))
        
        self.k_dist_var = tk.StringVar(value="")
        self.k_dist_label = ttk.Label(self.k_dist_frame, textvariable=self.k_dist_var, justify="left")
        self.k_dist_label.grid(row=0, column=1, sticky="w")
        
        # Detailed results text area
        self.text_frame = ttk.Frame(self.results_frame)
        self.results_text = scrolledtext.ScrolledText(self.text_frame, height=15, width=80, 
                                                     wrap=tk.WORD, font=("Consolas", 9))
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        
    def setup_layout(self):
        """设置布局"""
        
        # Main frame
        self.main_frame.pack(fill="both", expand=True)
        
        # File selection frame
        self.file_frame.pack(fill="x", pady=(0, 10))
        self.file_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        self.browse_button.pack(side="right", padx=(0, 5))
        self.load_button.pack(side="right")
        
        # Configuration frame
        self.config_frame.pack(fill="x", pady=(0, 10))
        self.indicators_frame.pack(fill="x", pady=(0, 5))
        self.depth_frame.pack(fill="x")
        
        # Control frame
        self.control_frame.pack(fill="x", pady=(0, 10))
        self.analyze_button.pack(side="left", padx=(0, 5))
        self.save_button.pack(side="left", padx=(0, 5))
        self.clear_button.pack(side="left", padx=(0, 5))
        self.config_button.pack(side="left", padx=(0, 5))
        self.save_config_button.pack(side="left", padx=(0, 20))
        self.progress_label.pack(side="left", padx=(0, 10))
        self.progress_bar.pack(side="left", fill="x", expand=True)
        
        # Results frame
        self.results_frame.pack(fill="both", expand=True)
        self.summary_frame.pack(fill="x", pady=(0, 5))
        self.k_dist_frame.pack(fill="x", pady=(0, 5))
        self.text_frame.pack(fill="both", expand=True)
        self.results_text.pack(fill="both", expand=True)
        
        # Status bar
        self.status_frame.pack(fill="x", side="bottom")
        self.status_label.pack(fill="x")
        
    def set_default_values(self):
        """设置默认值"""
        self.update_status("就绪 - 请选择数据文件")
        
    def browse_file(self):
        """浏览文件"""
        file_types = [
            ("CSV files", "*.csv"),
            ("TSV files", "*.tsv"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )
        
        if filename:
            self.file_path_var.set(filename)
            self.current_file = filename
            self.update_status(f"已选择文件: {os.path.basename(filename)}")
            
    def load_data(self):
        """加载数据"""
        if not self.current_file or not os.path.exists(self.current_file):
            messagebox.showerror("错误", "请先选择有效的数据文件")
            return
            
        try:
            self.update_status("正在加载数据...")
            self.progress_var.set("加载中...")
            self.progress_bar.start()
            
            # Create analyzer and load data
            self.analyzer = GZTraditionalAnalyzer()
            success = self.analyzer.load_data_from_file(self.current_file)
            
            self.progress_bar.stop()
            
            if success:
                data_shape = self.analyzer.data_df.shape
                self.update_status(f"数据加载成功: {data_shape[0]} 行, {data_shape[1]} 列")
                self.progress_var.set("数据已加载")
                self.analyze_button.config(state="normal")
                
                # Show data preview
                preview = f"数据预览:\n"
                preview += f"文件: {os.path.basename(self.current_file)}\n"
                preview += f"数据形状: {data_shape}\n"
                preview += f"列名: {list(self.analyzer.data_df.columns)}\n\n"
                preview += "前5行数据:\n"
                preview += str(self.analyzer.data_df.head())
                
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, preview)
                
            else:
                self.update_status("数据加载失败")
                self.progress_var.set("加载失败")
                messagebox.showerror("错误", "数据加载失败，请检查文件格式")
                
        except Exception as e:
            self.progress_bar.stop()
            self.update_status("数据加载出错")
            self.progress_var.set("出错")
            messagebox.showerror("错误", f"数据加载出错: {str(e)}")
            
    def start_analysis(self):
        """开始分析"""
        if not self.analyzer or self.analyzer.data_df is None:
            messagebox.showerror("错误", "请先加载数据")
            return
            
        # Run analysis in a separate thread to avoid blocking GUI
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_analysis(self):
        """运行分析（在后台线程中）"""
        try:
            # Update GUI in main thread
            self.root.after(0, self.update_analysis_start)
            
            # Update analyzer configuration
            config = {
                'enabled_indicators': {
                    'speed': self.speed_var.get(),
                    'amplitude': self.amplitude_var.get(),
                    'energy': self.energy_var.get(),
                    'psd': self.psd_var.get()
                },
                'gz_depth_range': self.depth_range_var.get(),
                'gz_enable_depth_range': self.enable_depth_range_var.get()
            }
            
            self.analyzer.update_config(config)
            
            # Run analysis
            self.results = self.analyzer.run_analysis()
            
            # Update GUI with results in main thread
            self.root.after(0, self.update_analysis_complete)
            
        except Exception as e:
            # Handle error in main thread
            self.root.after(0, lambda: self.update_analysis_error(str(e)))
            
    def update_analysis_start(self):
        """更新分析开始状态"""
        self.update_status("正在进行GZ传统分析...")
        self.progress_var.set("分析中...")
        self.progress_bar.start()
        self.analyze_button.config(state="disabled")
        
    def update_analysis_complete(self):
        """更新分析完成状态"""
        self.progress_bar.stop()
        
        if self.results:
            # Update summary
            category = self.results.get('final_category', '未知')
            self.category_var.set(category)
            
            # Set category color
            if category == "I类桩":
                self.category_label.config(foreground="green")
            elif category == "II类桩":
                self.category_label.config(foreground="blue")
            elif category == "III类桩":
                self.category_label.config(foreground="orange")
            elif category == "IV类桩":
                self.category_label.config(foreground="red")
            else:
                self.category_label.config(foreground="black")
                
            sections_count = len(self.results.get('K_values', {}))
            self.sections_var.set(str(sections_count))
            
            # Update K-value distribution
            k_values = self.results.get('K_values', {})
            if k_values:
                k_counts = {}
                for k_val in k_values.values():
                    k_counts[k_val] = k_counts.get(k_val, 0) + 1
                
                k_dist_text = ""
                for k_val in sorted(k_counts.keys()):
                    count = k_counts[k_val]
                    percentage = (count / len(k_values)) * 100
                    k_dist_text += f"K={k_val}: {count}个 ({percentage:.1f}%)  "
                
                self.k_dist_var.set(k_dist_text)
            
            # Show detailed results
            detailed_report = self.analyzer.generate_detailed_report()
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, detailed_report)
            
            self.update_status(f"分析完成 - 结果: {category}")
            self.progress_var.set("分析完成")
            self.save_button.config(state="normal")
            
        else:
            self.update_status("分析失败")
            self.progress_var.set("分析失败")
            messagebox.showerror("错误", "分析失败，请检查数据和配置")
            
        self.analyze_button.config(state="normal")
        
    def update_analysis_error(self, error_msg):
        """更新分析错误状态"""
        self.progress_bar.stop()
        self.update_status("分析出错")
        self.progress_var.set("出错")
        self.analyze_button.config(state="normal")
        messagebox.showerror("分析错误", f"分析过程中出错: {error_msg}")
        
    def save_results(self):
        """保存结果"""
        if not self.results:
            messagebox.showerror("错误", "没有可保存的结果")
            return
            
        file_types = [
            ("Text files", "*.txt"),
            ("JSON files", "*.json"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]
        
        # Generate default filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_analysis_results_{timestamp}.txt"
        
        filename = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".txt",
            initialname=default_name,
            filetypes=file_types
        )
        
        if filename:
            try:
                # Determine format from extension
                ext = os.path.splitext(filename)[1].lower()
                if ext == '.json':
                    format_type = 'json'
                elif ext == '.csv':
                    format_type = 'csv'
                else:
                    format_type = 'txt'
                
                success = self.analyzer.save_results(filename, format_type)
                
                if success:
                    self.update_status(f"结果已保存: {os.path.basename(filename)}")
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存结果失败")
                    
            except Exception as e:
                messagebox.showerror("错误", f"保存结果时出错: {str(e)}")
                
    def clear_results(self):
        """清除结果"""
        self.results_text.delete(1.0, tk.END)
        self.category_var.set("未分析")
        self.sections_var.set("0")
        self.k_dist_var.set("")
        self.category_label.config(foreground="black")
        self.progress_var.set("就绪")
        self.save_button.config(state="disabled")
        self.results = None
        self.update_status("结果已清除")
        
    def load_config(self):
        """加载配置文件"""
        file_types = [("JSON files", "*.json"), ("All files", "*.*")]

        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Update GUI with loaded configuration
                enabled_indicators = config.get('enabled_indicators', {})
                self.speed_var.set(enabled_indicators.get('speed', True))
                self.amplitude_var.set(enabled_indicators.get('amplitude', True))
                self.energy_var.set(enabled_indicators.get('energy', True))
                self.psd_var.set(enabled_indicators.get('psd', False))

                self.depth_range_var.set(config.get('gz_depth_range', 0.5))
                self.enable_depth_range_var.set(config.get('gz_enable_depth_range', True))

                self.update_status(f"配置已加载: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已加载: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        config = {
            'enabled_indicators': {
                'speed': self.speed_var.get(),
                'amplitude': self.amplitude_var.get(),
                'energy': self.energy_var.get(),
                'psd': self.psd_var.get()
            },
            'gz_depth_range': self.depth_range_var.get(),
            'gz_enable_depth_range': self.enable_depth_range_var.get(),
            'saved_time': datetime.now().isoformat(),
            'description': 'GZ Traditional Analysis Configuration'
        }

        file_types = [("JSON files", "*.json"), ("All files", "*.*")]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_config_{timestamp}.json"

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                self.update_status(f"配置已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已保存: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(f"{datetime.now().strftime('%H:%M:%S')} - {message}")


def main():
    """主函数"""
    root = tk.Tk()
    GZAnalysisGUI(root)
    
    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')  # You can add an icon file
    except:
        pass
    
    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
