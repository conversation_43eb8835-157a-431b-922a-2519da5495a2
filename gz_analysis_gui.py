#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GZ Traditional Analysis GUI
GZ传统分析图形用户界面

A user-friendly GUI for GZ Traditional Analysis functionality.
基于tkinter的GZ传统分析图形界面。

Author: Pile Integrity Analysis System
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import json
import glob
from datetime import datetime
import pandas as pd

# Import the GZ analysis module
try:
    from gz_traditional_analysis import GZTraditionalAnalyzer
except ImportError:
    messagebox.showerror("错误", "无法导入 gz_traditional_analysis 模块。请确保文件在同一目录中。")
    sys.exit(1)

class GZAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GZ Traditional Analysis - 桩基完整性分析")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.analyzer = None
        self.current_file = None
        self.current_folder = None
        self.selected_files = []
        self.results = None
        self.batch_results = {}
        
        # Create GUI components
        self.create_widgets()
        self.setup_layout()
        
        # Set default values
        self.set_default_values()
        
    def create_widgets(self):
        """创建GUI组件"""
        
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # File selection frame
        self.file_frame = ttk.LabelFrame(self.main_frame, text="批量数据文件选择", padding="10")

        # Folder selection
        self.folder_frame = ttk.Frame(self.file_frame)
        self.folder_path_var = tk.StringVar()
        self.folder_entry = ttk.Entry(self.folder_frame, textvariable=self.folder_path_var, width=50)
        self.browse_folder_button = ttk.Button(self.folder_frame, text="选择文件夹", command=self.browse_folder)
        self.scan_button = ttk.Button(self.folder_frame, text="扫描文件", command=self.scan_files)

        # File list
        self.file_list_frame = ttk.Frame(self.file_frame)
        ttk.Label(self.file_list_frame, text="找到的 *_with_energy.txt 文件:").pack(anchor="w")

        # File listbox with scrollbar
        self.file_listbox_frame = ttk.Frame(self.file_list_frame)
        self.file_listbox = tk.Listbox(self.file_listbox_frame, height=6, selectmode=tk.EXTENDED)
        self.file_scrollbar = ttk.Scrollbar(self.file_listbox_frame, orient="vertical", command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=self.file_scrollbar.set)

        # File list control buttons
        self.file_control_frame = ttk.Frame(self.file_list_frame)
        self.select_all_button = ttk.Button(self.file_control_frame, text="全选", command=self.select_all_files)
        self.deselect_all_button = ttk.Button(self.file_control_frame, text="全不选", command=self.deselect_all_files)
        self.load_button = ttk.Button(self.file_control_frame, text="加载选中文件", command=self.load_selected_files)
        
        # Configuration frame
        self.config_frame = ttk.LabelFrame(self.main_frame, text="分析配置", padding="10")
        
        # Indicators configuration
        self.indicators_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.indicators_frame, text="启用指标:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.speed_var = tk.BooleanVar(value=True)
        self.amplitude_var = tk.BooleanVar(value=True)
        self.energy_var = tk.BooleanVar(value=True)
        self.psd_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(self.indicators_frame, text="声速", variable=self.speed_var).grid(row=0, column=1, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="波幅", variable=self.amplitude_var).grid(row=0, column=2, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="能量", variable=self.energy_var).grid(row=0, column=3, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="PSD", variable=self.psd_var).grid(row=0, column=4, padx=5)
        
        # Depth range configuration
        self.depth_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.depth_frame, text="深度范围(m):").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.depth_range_var = tk.DoubleVar(value=0.5)
        self.depth_spinbox = ttk.Spinbox(self.depth_frame, from_=0.1, to=2.0, increment=0.1, 
                                        textvariable=self.depth_range_var, width=10)
        self.depth_spinbox.grid(row=0, column=1, padx=5)
        
        self.enable_depth_range_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(self.depth_frame, text="启用深度范围", 
                       variable=self.enable_depth_range_var).grid(row=0, column=2, padx=10)
        
        # Control buttons frame
        self.control_frame = ttk.Frame(self.main_frame)

        self.analyze_button = ttk.Button(self.control_frame, text="开始批量分析",
                                        command=self.start_batch_analysis, state="disabled")
        self.save_button = ttk.Button(self.control_frame, text="保存批量结果",
                                     command=self.save_batch_results, state="disabled")
        self.clear_button = ttk.Button(self.control_frame, text="清除结果", command=self.clear_results)
        self.config_button = ttk.Button(self.control_frame, text="加载配置", command=self.load_config)
        self.save_config_button = ttk.Button(self.control_frame, text="保存配置", command=self.save_config)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(self.control_frame, textvariable=self.progress_var)
        self.progress_bar = ttk.Progressbar(self.control_frame, mode='determinate')

        # Batch progress info
        self.batch_progress_var = tk.StringVar(value="")
        self.batch_progress_label = ttk.Label(self.control_frame, textvariable=self.batch_progress_var)
        
        # Results frame
        self.results_frame = ttk.LabelFrame(self.main_frame, text="批量分析结果", padding="10")

        # Batch results summary
        self.summary_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.summary_frame, text="已分析文件数:").grid(row=0, column=0, sticky="w", padx=(0, 10))

        self.analyzed_files_var = tk.StringVar(value="0")
        self.analyzed_files_label = ttk.Label(self.summary_frame, textvariable=self.analyzed_files_var,
                                             font=("Arial", 12, "bold"))
        self.analyzed_files_label.grid(row=0, column=1, sticky="w")

        ttk.Label(self.summary_frame, text="总截面数:").grid(row=0, column=2, sticky="w", padx=(20, 10))
        self.total_sections_var = tk.StringVar(value="0")
        ttk.Label(self.summary_frame, textvariable=self.total_sections_var).grid(row=0, column=3, sticky="w")
        
        # Batch category distribution
        self.category_dist_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.category_dist_frame, text="桩类分布:").grid(row=0, column=0, sticky="nw", padx=(0, 10))

        self.category_dist_var = tk.StringVar(value="")
        self.category_dist_label = ttk.Label(self.category_dist_frame, textvariable=self.category_dist_var, justify="left")
        self.category_dist_label.grid(row=0, column=1, sticky="w")
        
        # Detailed results text area
        self.text_frame = ttk.Frame(self.results_frame)
        self.results_text = scrolledtext.ScrolledText(self.text_frame, height=15, width=80, 
                                                     wrap=tk.WORD, font=("Consolas", 9))
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        
    def setup_layout(self):
        """设置布局"""
        
        # Main frame
        self.main_frame.pack(fill="both", expand=True)
        
        # File selection frame
        self.file_frame.pack(fill="x", pady=(0, 10))

        # Folder selection layout
        self.folder_frame.pack(fill="x", pady=(0, 5))
        self.folder_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        self.browse_folder_button.pack(side="right", padx=(0, 5))
        self.scan_button.pack(side="right")

        # File list layout
        self.file_list_frame.pack(fill="both", expand=True)
        self.file_listbox_frame.pack(fill="both", expand=True, pady=(5, 5))
        self.file_listbox.pack(side="left", fill="both", expand=True)
        self.file_scrollbar.pack(side="right", fill="y")

        # File control buttons layout
        self.file_control_frame.pack(fill="x")
        self.select_all_button.pack(side="left", padx=(0, 5))
        self.deselect_all_button.pack(side="left", padx=(0, 5))
        self.load_button.pack(side="right")
        
        # Configuration frame
        self.config_frame.pack(fill="x", pady=(0, 10))
        self.indicators_frame.pack(fill="x", pady=(0, 5))
        self.depth_frame.pack(fill="x")
        
        # Control frame
        self.control_frame.pack(fill="x", pady=(0, 10))
        self.analyze_button.pack(side="left", padx=(0, 5))
        self.save_button.pack(side="left", padx=(0, 5))
        self.clear_button.pack(side="left", padx=(0, 5))
        self.config_button.pack(side="left", padx=(0, 5))
        self.save_config_button.pack(side="left", padx=(0, 20))
        self.progress_label.pack(side="left", padx=(0, 10))
        self.batch_progress_label.pack(side="left", padx=(0, 10))
        self.progress_bar.pack(side="left", fill="x", expand=True)
        
        # Results frame
        self.results_frame.pack(fill="both", expand=True)
        self.summary_frame.pack(fill="x", pady=(0, 5))
        self.category_dist_frame.pack(fill="x", pady=(0, 5))
        self.text_frame.pack(fill="both", expand=True)
        self.results_text.pack(fill="both", expand=True)
        
        # Status bar
        self.status_frame.pack(fill="x", side="bottom")
        self.status_label.pack(fill="x")
        
    def set_default_values(self):
        """设置默认值"""
        self.update_status("就绪 - 请选择包含 *_with_energy.txt 文件的文件夹")

    def browse_folder(self):
        """浏览文件夹"""
        folder_path = filedialog.askdirectory(
            title="选择包含 *_with_energy.txt 文件的文件夹",
            initialdir=os.getcwd()
        )

        if folder_path:
            self.folder_path_var.set(folder_path)
            self.current_folder = folder_path
            self.update_status(f"已选择文件夹: {os.path.basename(folder_path)}")
            # 自动扫描文件
            self.scan_files()

    def scan_files(self):
        """扫描文件夹中的 *_with_energy.txt 文件"""
        if not self.current_folder or not os.path.exists(self.current_folder):
            messagebox.showerror("错误", "请先选择有效的文件夹")
            return

        try:
            # 搜索符合条件的文件
            pattern = os.path.join(self.current_folder, "*_with_energy.txt")
            found_files = glob.glob(pattern)

            # 清空文件列表
            self.file_listbox.delete(0, tk.END)
            self.selected_files = []

            if found_files:
                # 按文件名排序
                found_files.sort()

                # 添加到列表框
                for file_path in found_files:
                    filename = os.path.basename(file_path)
                    self.file_listbox.insert(tk.END, filename)
                    self.selected_files.append(file_path)

                # 默认全选
                self.select_all_files()

                self.update_status(f"找到 {len(found_files)} 个 *_with_energy.txt 文件")
            else:
                self.update_status("未找到 *_with_energy.txt 文件")
                messagebox.showinfo("提示", f"在文件夹 {self.current_folder} 中未找到 *_with_energy.txt 文件")

        except Exception as e:
            messagebox.showerror("错误", f"扫描文件时出错: {str(e)}")

    def select_all_files(self):
        """全选文件"""
        self.file_listbox.select_set(0, tk.END)

    def deselect_all_files(self):
        """全不选文件"""
        self.file_listbox.selection_clear(0, tk.END)

    def load_selected_files(self):
        """加载选中的文件"""
        selected_indices = self.file_listbox.curselection()

        if not selected_indices:
            messagebox.showerror("错误", "请至少选择一个文件")
            return

        # 获取选中的文件路径
        self.selected_files = []
        for index in selected_indices:
            filename = self.file_listbox.get(index)
            file_path = os.path.join(self.current_folder, filename)
            self.selected_files.append(file_path)

        self.update_status(f"已选择 {len(self.selected_files)} 个文件进行分析")
        self.analyze_button.config(state="normal")

        # 显示选中文件的预览
        preview = f"选中的文件 ({len(self.selected_files)} 个):\n\n"
        for i, file_path in enumerate(self.selected_files, 1):
            filename = os.path.basename(file_path)
            preview += f"{i}. {filename}\n"

        preview += f"\n准备进行批量分析..."

        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, preview)

    def browse_file(self):
        """浏览文件"""
        file_types = [
            ("CSV files", "*.csv"),
            ("TSV files", "*.tsv"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )
        
        if filename:
            self.file_path_var.set(filename)
            self.current_file = filename
            self.update_status(f"已选择文件: {os.path.basename(filename)}")
            
    def load_data(self):
        """加载数据"""
        if not self.current_file or not os.path.exists(self.current_file):
            messagebox.showerror("错误", "请先选择有效的数据文件")
            return
            
        try:
            self.update_status("正在加载数据...")
            self.progress_var.set("加载中...")
            self.progress_bar.start()
            
            # Create analyzer and load data
            self.analyzer = GZTraditionalAnalyzer()
            success = self.analyzer.load_data_from_file(self.current_file)
            
            self.progress_bar.stop()
            
            if success:
                data_shape = self.analyzer.data_df.shape
                self.update_status(f"数据加载成功: {data_shape[0]} 行, {data_shape[1]} 列")
                self.progress_var.set("数据已加载")
                self.analyze_button.config(state="normal")
                
                # Show data preview
                preview = f"数据预览:\n"
                preview += f"文件: {os.path.basename(self.current_file)}\n"
                preview += f"数据形状: {data_shape}\n"
                preview += f"列名: {list(self.analyzer.data_df.columns)}\n\n"
                preview += "前5行数据:\n"
                preview += str(self.analyzer.data_df.head())
                
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, preview)
                
            else:
                self.update_status("数据加载失败")
                self.progress_var.set("加载失败")
                messagebox.showerror("错误", "数据加载失败，请检查文件格式")
                
        except Exception as e:
            self.progress_bar.stop()
            self.update_status("数据加载出错")
            self.progress_var.set("出错")
            messagebox.showerror("错误", f"数据加载出错: {str(e)}")
            
    def start_batch_analysis(self):
        """开始批量分析"""
        if not self.selected_files:
            messagebox.showerror("错误", "请先选择要分析的文件")
            return

        # Run batch analysis in a separate thread to avoid blocking GUI
        thread = threading.Thread(target=self.run_batch_analysis)
        thread.daemon = True
        thread.start()

    def start_analysis(self):
        """开始单文件分析（保留兼容性）"""
        if not self.analyzer or self.analyzer.data_df is None:
            messagebox.showerror("错误", "请先加载数据")
            return

        # Run analysis in a separate thread to avoid blocking GUI
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_batch_analysis(self):
        """运行批量分析（在后台线程中）"""
        try:
            # Update GUI in main thread
            self.root.after(0, self.update_batch_analysis_start)

            # 清空之前的批量结果
            self.batch_results = {}
            total_files = len(self.selected_files)

            # 获取分析配置
            config = {
                'enabled_indicators': {
                    'speed': self.speed_var.get(),
                    'amplitude': self.amplitude_var.get(),
                    'energy': self.energy_var.get(),
                    'psd': self.psd_var.get()
                },
                'gz_depth_range': self.depth_range_var.get(),
                'gz_enable_depth_range': self.enable_depth_range_var.get()
            }

            # 逐个分析文件
            for i, file_path in enumerate(self.selected_files):
                filename = os.path.basename(file_path)

                # 更新进度
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress, f=filename, idx=i+1, total=total_files:
                               self.update_batch_progress(p, f, idx, total))

                try:
                    # 创建新的分析器实例
                    analyzer = GZTraditionalAnalyzer()
                    analyzer.update_config(config)

                    # 加载数据
                    if analyzer.load_data_from_file(file_path):
                        # 运行分析
                        result = analyzer.run_analysis()
                        if result:
                            self.batch_results[filename] = {
                                'file_path': file_path,
                                'result': result,
                                'analyzer': analyzer,
                                'status': 'success'
                            }
                        else:
                            self.batch_results[filename] = {
                                'file_path': file_path,
                                'result': None,
                                'analyzer': None,
                                'status': 'analysis_failed',
                                'error': '分析失败'
                            }
                    else:
                        self.batch_results[filename] = {
                            'file_path': file_path,
                            'result': None,
                            'analyzer': None,
                            'status': 'load_failed',
                            'error': '数据加载失败'
                        }

                except Exception as e:
                    self.batch_results[filename] = {
                        'file_path': file_path,
                        'result': None,
                        'analyzer': None,
                        'status': 'error',
                        'error': str(e)
                    }

            # 完成进度更新
            self.root.after(0, lambda: self.update_batch_progress(100, "完成", total_files, total_files))

            # Update GUI with results in main thread
            self.root.after(0, self.update_batch_analysis_complete)

        except Exception as e:
            # Handle error in main thread
            self.root.after(0, lambda: self.update_batch_analysis_error(str(e)))

    def run_analysis(self):
        """运行单文件分析（在后台线程中）"""
        try:
            # Update GUI in main thread
            self.root.after(0, self.update_analysis_start)

            # Update analyzer configuration
            config = {
                'enabled_indicators': {
                    'speed': self.speed_var.get(),
                    'amplitude': self.amplitude_var.get(),
                    'energy': self.energy_var.get(),
                    'psd': self.psd_var.get()
                },
                'gz_depth_range': self.depth_range_var.get(),
                'gz_enable_depth_range': self.enable_depth_range_var.get()
            }

            self.analyzer.update_config(config)

            # Run analysis
            self.results = self.analyzer.run_analysis()

            # Update GUI with results in main thread
            self.root.after(0, self.update_analysis_complete)

        except Exception as e:
            # Handle error in main thread
            self.root.after(0, lambda: self.update_analysis_error(str(e)))
            
    def update_batch_analysis_start(self):
        """更新批量分析开始状态"""
        self.update_status("正在进行批量GZ传统分析...")
        self.progress_var.set("批量分析中...")
        self.batch_progress_var.set("准备中...")
        self.progress_bar.config(mode='determinate')
        self.progress_bar['value'] = 0
        self.analyze_button.config(state="disabled")

    def update_batch_progress(self, progress, current_file, current_index, total_files):
        """更新批量分析进度"""
        self.progress_bar['value'] = progress
        self.batch_progress_var.set(f"({current_index}/{total_files}) {current_file}")

    def update_batch_analysis_complete(self):
        """更新批量分析完成状态"""
        self.progress_bar['value'] = 100

        if self.batch_results:
            # 统计结果
            total_files = len(self.batch_results)
            successful_files = sum(1 for r in self.batch_results.values() if r['status'] == 'success')

            # 统计桩类分布
            category_counts = {}
            total_sections = 0

            for filename, result_data in self.batch_results.items():
                if result_data['status'] == 'success' and result_data['result']:
                    category = result_data['result'].get('final_category', '未知')
                    category_counts[category] = category_counts.get(category, 0) + 1

                    # 统计截面数
                    k_values = result_data['result'].get('K_values', {})
                    total_sections += len(k_values)

            # 更新汇总信息
            self.analyzed_files_var.set(f"{successful_files}/{total_files}")
            self.total_sections_var.set(str(total_sections))

            # 更新桩类分布
            if category_counts:
                category_dist_text = ""
                for category in sorted(category_counts.keys()):
                    count = category_counts[category]
                    percentage = (count / successful_files) * 100 if successful_files > 0 else 0
                    category_dist_text += f"{category}: {count}个 ({percentage:.1f}%)  "
                self.category_dist_var.set(category_dist_text)

            # 生成详细报告
            detailed_report = self.generate_batch_report()
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, detailed_report)

            self.update_status(f"批量分析完成 - 成功分析 {successful_files}/{total_files} 个文件")
            self.progress_var.set("批量分析完成")
            self.batch_progress_var.set("完成")
            self.save_button.config(state="normal")

        else:
            self.update_status("批量分析失败")
            self.progress_var.set("批量分析失败")
            self.batch_progress_var.set("失败")
            messagebox.showerror("错误", "批量分析失败，请检查文件和配置")

        self.analyze_button.config(state="normal")

    def update_batch_analysis_error(self, error_msg):
        """更新批量分析错误状态"""
        self.progress_bar['value'] = 0
        self.update_status("批量分析出错")
        self.progress_var.set("出错")
        self.batch_progress_var.set("出错")
        self.analyze_button.config(state="normal")
        messagebox.showerror("批量分析错误", f"批量分析过程中出错: {error_msg}")

    def update_analysis_start(self):
        """更新单文件分析开始状态"""
        self.update_status("正在进行GZ传统分析...")
        self.progress_var.set("分析中...")
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()
        self.analyze_button.config(state="disabled")
        
    def update_analysis_complete(self):
        """更新分析完成状态"""
        self.progress_bar.stop()
        
        if self.results:
            # Update summary
            category = self.results.get('final_category', '未知')
            self.category_var.set(category)
            
            # Set category color
            if category == "I类桩":
                self.category_label.config(foreground="green")
            elif category == "II类桩":
                self.category_label.config(foreground="blue")
            elif category == "III类桩":
                self.category_label.config(foreground="orange")
            elif category == "IV类桩":
                self.category_label.config(foreground="red")
            else:
                self.category_label.config(foreground="black")
                
            sections_count = len(self.results.get('K_values', {}))
            self.sections_var.set(str(sections_count))
            
            # Update K-value distribution
            k_values = self.results.get('K_values', {})
            if k_values:
                k_counts = {}
                for k_val in k_values.values():
                    k_counts[k_val] = k_counts.get(k_val, 0) + 1
                
                k_dist_text = ""
                for k_val in sorted(k_counts.keys()):
                    count = k_counts[k_val]
                    percentage = (count / len(k_values)) * 100
                    k_dist_text += f"K={k_val}: {count}个 ({percentage:.1f}%)  "
                
                self.k_dist_var.set(k_dist_text)
            
            # Show detailed results
            detailed_report = self.analyzer.generate_detailed_report()
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, detailed_report)
            
            self.update_status(f"分析完成 - 结果: {category}")
            self.progress_var.set("分析完成")
            self.save_button.config(state="normal")
            
        else:
            self.update_status("分析失败")
            self.progress_var.set("分析失败")
            messagebox.showerror("错误", "分析失败，请检查数据和配置")
            
        self.analyze_button.config(state="normal")

    def generate_batch_report(self):
        """生成批量分析报告"""
        if not self.batch_results:
            return "无批量分析结果"

        report = "=" * 80 + "\n"
        report += "批量GZ传统分析报告\n"
        report += "=" * 80 + "\n\n"

        # 汇总信息
        total_files = len(self.batch_results)
        successful_files = sum(1 for r in self.batch_results.values() if r['status'] == 'success')
        failed_files = total_files - successful_files

        report += f"分析文件总数: {total_files}\n"
        report += f"成功分析: {successful_files}\n"
        report += f"分析失败: {failed_files}\n"
        report += f"成功率: {(successful_files/total_files)*100:.1f}%\n\n"

        # 桩类统计
        category_counts = {}
        for result_data in self.batch_results.values():
            if result_data['status'] == 'success' and result_data['result']:
                category = result_data['result'].get('final_category', '未知')
                category_counts[category] = category_counts.get(category, 0) + 1

        if category_counts:
            report += "桩类分布统计:\n"
            report += "-" * 40 + "\n"
            for category in sorted(category_counts.keys()):
                count = category_counts[category]
                percentage = (count / successful_files) * 100 if successful_files > 0 else 0
                report += f"{category}: {count}个 ({percentage:.1f}%)\n"
            report += "\n"

        # 详细结果
        report += "详细分析结果:\n"
        report += "-" * 80 + "\n"

        for filename, result_data in self.batch_results.items():
            report += f"\n文件: {filename}\n"

            if result_data['status'] == 'success':
                result = result_data['result']
                category = result.get('final_category', '未知')
                k_values = result.get('K_values', {})
                sections_count = len(k_values)

                report += f"  状态: 分析成功\n"
                report += f"  桩类判定: {category}\n"
                report += f"  分析截面数: {sections_count}\n"

                if k_values:
                    k_counts = {}
                    for k_val in k_values.values():
                        k_counts[k_val] = k_counts.get(k_val, 0) + 1

                    k_dist = ", ".join([f"K={k}({count}个)" for k, count in sorted(k_counts.items())])
                    report += f"  K值分布: {k_dist}\n"

            else:
                report += f"  状态: {result_data['status']}\n"
                if 'error' in result_data:
                    report += f"  错误信息: {result_data['error']}\n"

        report += "\n" + "=" * 80 + "\n"
        report += f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

        return report

    def update_analysis_error(self, error_msg):
        """更新分析错误状态"""
        self.progress_bar.stop()
        self.update_status("分析出错")
        self.progress_var.set("出错")
        self.analyze_button.config(state="normal")
        messagebox.showerror("分析错误", f"分析过程中出错: {error_msg}")
        
    def save_batch_results(self):
        """保存批量分析结果"""
        if not self.batch_results:
            messagebox.showerror("错误", "没有可保存的批量分析结果")
            return

        file_types = [
            ("Text files", "*.txt"),
            ("JSON files", "*.json"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]

        # Generate default filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"batch_gz_analysis_results_{timestamp}.txt"

        filename = filedialog.asksaveasfilename(
            title="保存批量分析结果",
            defaultextension=".txt",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                # Determine format from extension
                ext = os.path.splitext(filename)[1].lower()

                if ext == '.json':
                    self.save_batch_results_json(filename)
                elif ext == '.csv':
                    self.save_batch_results_csv(filename)
                else:
                    self.save_batch_results_txt(filename)

                self.update_status(f"批量结果已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"批量分析结果已保存到: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存批量结果时出错: {str(e)}")

    def save_batch_results_txt(self, filename):
        """保存批量结果为TXT格式"""
        report = self.generate_batch_report()
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

    def save_batch_results_json(self, filename):
        """保存批量结果为JSON格式"""
        json_data = {
            'analysis_time': datetime.now().isoformat(),
            'total_files': len(self.batch_results),
            'successful_files': sum(1 for r in self.batch_results.values() if r['status'] == 'success'),
            'results': {}
        }

        for filename_key, result_data in self.batch_results.items():
            json_data['results'][filename_key] = {
                'file_path': result_data['file_path'],
                'status': result_data['status'],
                'result': result_data['result'] if result_data['status'] == 'success' else None,
                'error': result_data.get('error', None)
            }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

    def save_batch_results_csv(self, filename):
        """保存批量结果为CSV格式"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入标题行
            writer.writerow(['文件名', '状态', '桩类判定', '截面数', 'K值分布', '错误信息'])

            # 写入数据行
            for filename_key, result_data in self.batch_results.items():
                if result_data['status'] == 'success' and result_data['result']:
                    result = result_data['result']
                    category = result.get('final_category', '未知')
                    k_values = result.get('K_values', {})
                    sections_count = len(k_values)

                    # 计算K值分布
                    k_counts = {}
                    for k_val in k_values.values():
                        k_counts[k_val] = k_counts.get(k_val, 0) + 1
                    k_dist = ", ".join([f"K={k}({count})" for k, count in sorted(k_counts.items())])

                    writer.writerow([filename_key, '成功', category, sections_count, k_dist, ''])
                else:
                    error_msg = result_data.get('error', '未知错误')
                    writer.writerow([filename_key, '失败', '', '', '', error_msg])

    def save_results(self):
        """保存单文件结果（保留兼容性）"""
        if not self.results:
            messagebox.showerror("错误", "没有可保存的结果")
            return

        file_types = [
            ("Text files", "*.txt"),
            ("JSON files", "*.json"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]

        # Generate default filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_analysis_results_{timestamp}.txt"

        filename = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".txt",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                # Determine format from extension
                ext = os.path.splitext(filename)[1].lower()
                if ext == '.json':
                    format_type = 'json'
                elif ext == '.csv':
                    format_type = 'csv'
                else:
                    format_type = 'txt'

                success = self.analyzer.save_results(filename, format_type)

                if success:
                    self.update_status(f"结果已保存: {os.path.basename(filename)}")
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存结果失败")

            except Exception as e:
                messagebox.showerror("错误", f"保存结果时出错: {str(e)}")
                
    def clear_results(self):
        """清除结果"""
        self.results_text.delete(1.0, tk.END)

        # 清除单文件结果显示
        self.category_var.set("未分析")
        self.sections_var.set("0")

        # 清除批量结果显示
        self.analyzed_files_var.set("0")
        self.total_sections_var.set("0")
        self.category_dist_var.set("")

        self.category_label.config(foreground="black")
        self.progress_var.set("就绪")
        self.batch_progress_var.set("")
        self.save_button.config(state="disabled")
        self.analyze_button.config(state="disabled")

        # 重置进度条
        self.progress_bar['value'] = 0

        # 清除数据
        self.results = None
        self.batch_results = {}
        self.selected_files = []

        # 清空文件列表
        self.file_listbox.delete(0, tk.END)

        self.update_status("结果已清除")
        
    def load_config(self):
        """加载配置文件"""
        file_types = [("JSON files", "*.json"), ("All files", "*.*")]

        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Update GUI with loaded configuration
                enabled_indicators = config.get('enabled_indicators', {})
                self.speed_var.set(enabled_indicators.get('speed', True))
                self.amplitude_var.set(enabled_indicators.get('amplitude', True))
                self.energy_var.set(enabled_indicators.get('energy', True))
                self.psd_var.set(enabled_indicators.get('psd', False))

                self.depth_range_var.set(config.get('gz_depth_range', 0.5))
                self.enable_depth_range_var.set(config.get('gz_enable_depth_range', True))

                self.update_status(f"配置已加载: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已加载: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        config = {
            'enabled_indicators': {
                'speed': self.speed_var.get(),
                'amplitude': self.amplitude_var.get(),
                'energy': self.energy_var.get(),
                'psd': self.psd_var.get()
            },
            'gz_depth_range': self.depth_range_var.get(),
            'gz_enable_depth_range': self.enable_depth_range_var.get(),
            'saved_time': datetime.now().isoformat(),
            'description': 'GZ Traditional Analysis Configuration'
        }

        file_types = [("JSON files", "*.json"), ("All files", "*.*")]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_config_{timestamp}.json"

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                self.update_status(f"配置已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已保存: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(f"{datetime.now().strftime('%H:%M:%S')} - {message}")


def main():
    """主函数"""
    root = tk.Tk()
    GZAnalysisGUI(root)
    
    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')  # You can add an icon file
    except:
        pass
    
    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
