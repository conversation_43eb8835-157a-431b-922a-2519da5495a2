#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GZ Traditional Analysis GUI
GZ传统分析图形用户界面

A user-friendly GUI for GZ Traditional Analysis functionality.
基于tkinter的GZ传统分析图形界面。

Author: Pile Integrity Analysis System
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import json
import glob
from datetime import datetime
import pandas as pd
import numpy as np
import traceback
import argparse

# --- GZ Method Core Configuration ---
DEFAULT_GZ_CONFIG = {
    'Sp_conditions': {
        'ge_100': lambda sp: sp >= 100, '85_lt_100': lambda sp: 85 <= sp < 100,
        '75_lt_85': lambda sp: 75 <= sp < 85, '65_lt_75': lambda sp: 65 <= sp < 75,
        'lt_65': lambda sp: sp < 65, 'ge_85': lambda sp: sp >= 85,
        'ge_75': lambda sp: sp >= 75, 'ge_65': lambda sp: sp >= 65,
    },
    'Ad_conditions': {
        'le_0': lambda ad: ad <= 0, 'gt_0_le_4': lambda ad: 0 < ad <= 4,
        'gt_4_le_8': lambda ad: 4 < ad <= 8, 'gt_8_le_12': lambda ad: 8 < ad <= 12,
        'gt_12': lambda ad: ad > 12, 'le_4': lambda ad: ad <= 4,
        'le_8': lambda ad: ad <= 8, 'le_12': lambda ad: ad <= 12,
    },
    'Energy_conditions': {
        'I_1': lambda e: 0.8 <= e <= 100.0,  # I(j,i)=1: 正常
        'I_2': lambda e: 0.5 <= e < 0.8,      # I(j,i)=2: 轻微畸变
        'I_3': lambda e: 0.25 <= e < 0.5,     # I(j,i)=3: 明显畸变
        'I_4': lambda e: 0.0 <= e < 0.25,     # I(j,i)=4: 严重畸变
        'normal': lambda e: 0.8 <= e <= 100.0, 'light': lambda e: 0.5 <= e < 0.8,
        'obvious': lambda e: 0.25 <= e < 0.5, 'severe': lambda e: 0.0 <= e < 0.25,
    },
    'PSD_conditions': {
        'I_1': lambda p: 0.0 <= p <= 1.0,     # I(j,i)=1: 正常
        'I_2': lambda p: 1.0 < p <= 2.0,      # I(j,i)=2: 轻微畸变
        'I_3': lambda p: 2.0 < p <= 3.0,      # I(j,i)=3: 明显畸变
        'I_4': lambda p: 3.0 < p <= 100.0,    # I(j,i)=4: 严重畸变
        'normal': lambda p: 0.0 <= p <= 1.0, 'light': lambda p: 1.0 < p <= 2.0,
        'obvious': lambda p: 2.0 < p <= 3.0, 'severe': lambda p: 3.0 < p <= 100.0,
    },
    'Bi_ratio_conditions': {
        'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8,
        'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5,
        'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25,
    }
}

# --- Default Analysis Configuration ---
DEFAULT_ANALYSIS_CONFIG = {
    'enabled_indicators': {
        'speed': True,
        'amplitude': True,
        'energy': True,
        'psd': False
    },
    'gz_depth_range': 0.5,  # 深度范围(m)
    'gz_enable_depth_range': True,  # 是否启用深度范围
    'bi_ratio_default': 1.0,  # 默认Bi比值
}

class GZTraditionalAnalyzer:
    """GZ Traditional Analysis Core Engine"""

    def __init__(self, config=None):
        """
        Initialize GZ Traditional Analyzer

        Args:
            config (dict): Analysis configuration
        """
        self.config = config or DEFAULT_ANALYSIS_CONFIG.copy()
        self.gz_config = DEFAULT_GZ_CONFIG.copy()
        self.data_df = None
        self.results = None

    def load_data_from_file(self, file_path):
        """
        Load and parse data from file

        Args:
            file_path (str): Path to data file

        Returns:
            bool: True if successful, False otherwise
        """
        print(f"[FILE] Loading data file: {file_path}")
        try:
            self.data_df = self._parse_data_file(file_path)
            if self.data_df is None or self.data_df.empty:
                print("[ERROR] Data loading failed or file is empty")
                return False
            print(f"[SUCCESS] Data loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            return True
        except Exception as e:
            print(f"[ERROR] Data loading error: {str(e)}")
            traceback.print_exc()
            return False

    def load_data_from_dataframe(self, df):
        """
        Load data from pandas DataFrame

        Args:
            df (pd.DataFrame): Input data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.data_df = self._standardize_dataframe(df.copy())
            if self.data_df is None or self.data_df.empty:
                print("[ERROR] DataFrame is empty or invalid")
                return False
            print(f"[SUCCESS] DataFrame loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            return True
        except Exception as e:
            print(f"[ERROR] DataFrame loading error: {str(e)}")
            traceback.print_exc()
            return False

    def _parse_data_file(self, file_path):
        """Parse data file and standardize column names"""
        try:
            # Try different separators
            separators = ['\t', ',', ';', ' ']
            df = None

            for sep in separators:
                try:
                    df = pd.read_csv(file_path, sep=sep, header=0)
                    if len(df.columns) > 1:  # Successfully parsed
                        break
                except:
                    continue

            if df is None or len(df.columns) <= 1:
                raise ValueError("Unable to parse file with any separator")

            print(f"原始数据列名: {list(df.columns)}, 数据形状: {df.shape}")
            return self._standardize_dataframe(df)

        except Exception as e:
            print(f"数据解析错误: {str(e)}")
            traceback.print_exc()
            return None

    def _standardize_dataframe(self, df):
        """Standardize DataFrame column names and data types"""
        try:
            # Column mapping for standardization
            column_mapping = {}
            for col_name in df.columns:
                col_lower = col_name.lower().replace('_', '').replace(' ', '').replace('%', '')
                if 'depth' in col_lower:
                    column_mapping[col_name] = 'Depth'
                elif '1-2speed' in col_lower or '12speed' in col_lower:
                    column_mapping[col_name] = 'S1'
                elif '1-2amp' in col_lower or '12amp' in col_lower:
                    column_mapping[col_name] = 'A1'
                elif '1-3speed' in col_lower or '13speed' in col_lower:
                    column_mapping[col_name] = 'S2'
                elif '1-3amp' in col_lower or '13amp' in col_lower:
                    column_mapping[col_name] = 'A2'
                elif '2-3speed' in col_lower or '23speed' in col_lower:
                    column_mapping[col_name] = 'S3'
                elif '2-3amp' in col_lower or '23amp' in col_lower:
                    column_mapping[col_name] = 'A3'
                # Energy% columns
                elif '1-2energy' in col_lower or '12energy' in col_lower:
                    column_mapping[col_name] = 'E1'
                elif '1-3energy' in col_lower or '13energy' in col_lower:
                    column_mapping[col_name] = 'E2'
                elif '2-3energy' in col_lower or '23energy' in col_lower:
                    column_mapping[col_name] = 'E3'
                # PSD columns
                elif '1-2psd' in col_lower or '12psd' in col_lower:
                    column_mapping[col_name] = 'P1'
                elif '1-3psd' in col_lower or '13psd' in col_lower:
                    column_mapping[col_name] = 'P2'
                elif '2-3psd' in col_lower or '23psd' in col_lower:
                    column_mapping[col_name] = 'P3'

            df.rename(columns=column_mapping, inplace=True)
            print(f"重命名后列名: {list(df.columns)}")

            # Ensure required columns exist
            required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告：缺少列 {missing_columns}")

            # Add missing columns with NaN values
            for col in required_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                else:
                    df[col] = np.nan
                    print(f"Added missing column '{col}' with NaNs.")

            # Remove rows with missing depth values
            original_len = len(df)
            df.dropna(subset=['Depth'], inplace=True)
            print(f"处理缺失值后: {len(df)} 行 (原始: {original_len} 行)")

            return df

        except Exception as e:
            print(f"数据标准化错误: {str(e)}")
            traceback.print_exc()
            return None

    def calculate_I_ji_enhanced(self, Sp, Ad, Energy=None, PSD=None, enabled_indicators=None):
        """
        增强的I(j,i)计算函数，按照GZ Traditional Analysis公式
        I(j,i)=1：Speed%(100.0-1000.0)，Amp%(-100-3)，Energy%(0.8-100), PSD(0-1)
        I(j,i)=2：Speed%(85.0-100.0)，Amp%(3-6)，Energy%(0.5-0.8), PSD(1-2)
        I(j,i)=3：Speed%(75.0-85.0)，Amp%(6-12)，Energy%(0.25-0.5), PSD(2-3)
        I(j,i)=4：Speed%(65.0-75.0)，Amp%(12-100)，Energy%(0-0.25), PSD(3-100)
        """
        if enabled_indicators is None:
            enabled_indicators = self.config['enabled_indicators']

        # 计算各指标的I(j,i)值
        i_values = []

        # Speed% 分类
        if enabled_indicators.get('speed', True) and Sp is not None:
            if 100.0 <= Sp <= 1000.0:
                i_values.append(1)  # I(j,i)=1
            elif 85.0 <= Sp < 100.0:
                i_values.append(2)  # I(j,i)=2
            elif 75.0 <= Sp < 85.0:
                i_values.append(3)  # I(j,i)=3
            elif 65.0 <= Sp < 75.0:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # Amplitude 分类
        if enabled_indicators.get('amplitude', True) and Ad is not None:
            if -100 <= Ad <= 3:
                i_values.append(1)  # I(j,i)=1
            elif 3 < Ad <= 6:
                i_values.append(2)  # I(j,i)=2
            elif 6 < Ad <= 12:
                i_values.append(3)  # I(j,i)=3
            elif 12 < Ad <= 100:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # Energy% 分类
        if enabled_indicators.get('energy', False) and Energy is not None:
            if 0.8 <= Energy <= 100:
                i_values.append(1)  # I(j,i)=1
            elif 0.5 <= Energy < 0.8:
                i_values.append(2)  # I(j,i)=2
            elif 0.25 <= Energy < 0.5:
                i_values.append(3)  # I(j,i)=3
            elif 0 <= Energy < 0.25:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # PSD 分类
        if enabled_indicators.get('psd', False) and PSD is not None:
            if 0 <= PSD <= 1:
                i_values.append(1)  # I(j,i)=1
            elif 1 < PSD <= 2:
                i_values.append(2)  # I(j,i)=2
            elif 2 < PSD <= 3:
                i_values.append(3)  # I(j,i)=3
            elif 3 < PSD <= 100:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # 如果没有启用任何指标，返回1（正常）
        if not i_values:
            return 1

        # 取最严重的分类作为最终I(j,i)值
        return max(i_values)

    def calculate_K_i(self, I_ji_values_at_depth):
        """计算K(i)值"""
        if not I_ji_values_at_depth:
            return 0
        valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
        if not valid_I_ji:
            return 0
        sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)
        sum_I_ji = sum(valid_I_ji)
        if sum_I_ji == 0:
            return 0
        K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
        return int(K_i_float)

    def check_continuous_K_in_range(self, K_values_with_depths, target_K, depth_range=0.5, depth_interval=0.1):
        """
        检查是否存在某深度范围内K(i)值均为target_K的情况
        """
        if not K_values_with_depths:
            return False, -1

        sorted_k_data = sorted(K_values_with_depths.items())
        required_points = int(depth_range / depth_interval) + 1
        range_span = depth_range

        for i in range(len(sorted_k_data) - required_points + 1):
            start_depth = sorted_k_data[i][0]
            continuous_found = True
            depths_in_range = []

            for j in range(required_points):
                expected_depth = start_depth + j * depth_interval
                found_match = False
                for depth, k_val in sorted_k_data:
                    if abs(depth - expected_depth) <= depth_interval * 0.1:
                        if k_val == target_K:
                            depths_in_range.append(depth)
                            found_match = True
                            break
                        else:
                            continuous_found = False
                            break

                if not found_match:
                    continuous_found = False
                    break

            if continuous_found and len(depths_in_range) == required_points:
                actual_span = max(depths_in_range) - min(depths_in_range)
                if abs(actual_span - range_span) <= depth_interval:
                    return True, min(depths_in_range)

        return False, -1

    def determine_final_category(self, K_values_map_with_depths, gz_depth_range=0.5):
        """
        根据新的判定依据确定桩类
        """
        report_details = []
        if not K_values_map_with_depths:
            return "N/A", ["没有计算K值。"]

        K_values_list = list(K_values_map_with_depths.values())
        has_K4 = any(k == 4 for k in K_values_list)
        has_K3 = any(k == 3 for k in K_values_list)
        has_K2 = any(k == 2 for k in K_values_list)

        num_K4 = K_values_list.count(4)
        num_K3 = K_values_list.count(3)
        num_K2 = K_values_list.count(2)

        range_cm = int(gz_depth_range * 100)
        print(f"[DEBUG] K值统计: K=1:{K_values_list.count(1)}, K=2:{num_K2}, K=3:{num_K3}, K=4:{num_K4}")

        # 1、I类桩：所有检测截面Ki值均为1
        if all(k == 1 for k in K_values_list):
            report_details.append("所有检测截面Ki值均为1。")
            return "I类桩", report_details

        # 4、IV类桩判定
        if num_K4 == 1:
            k4_depths = [d for d, k in K_values_map_with_depths.items() if k == 4]
            report_details.append(f"所有检测截面仅存在一个K(i)=4的情况，深度为: {k4_depths[0]:.2f}m。")
            return "IV类桩", report_details

        if has_K3 and num_K3 > 1 and not has_K4:
            continuous_K3_found, k3_start_depth = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=3, depth_range=gz_depth_range)
            if continuous_K3_found:
                report_details.append(f"存在Ki=3的截面不止一个，且不存在Ki=4，且在深度{k3_start_depth:.2f}m开始的{range_cm}cm范围内K(i)值均为3。")
                return "IV类桩", report_details

        # 3、III类桩判定
        if num_K3 == 1 and not has_K4:
            k3_depths = [d for d, k in K_values_map_with_depths.items() if k == 3]
            report_details.append(f"所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4，深度为: {k3_depths[0]:.2f}m。")
            return "III类桩", report_details

        if has_K3 and num_K3 > 1 and not has_K4:
            k3_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 3])
            all_k3_separated = True
            for i in range(len(k3_depths) - 1):
                distance = k3_depths[i+1] - k3_depths[i]
                if distance < gz_depth_range:
                    all_k3_separated = False
                    break

            if all_k3_separated:
                report_details.append(f"存在Ki=3的截面不止一个({num_K3}个)，且不存在Ki=4，且任意两个相邻的Ki=3截面距离≥{range_cm}cm。")
                return "III类桩", report_details

        if has_K2 and num_K2 > 1 and not has_K3 and not has_K4:
            continuous_K2_found, k2_start_depth = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=2, depth_range=gz_depth_range)
            if continuous_K2_found:
                report_details.append(f"存在Ki=2的截面不止一个，且不存在Ki=3、Ki=4，且在深度{k2_start_depth:.2f}m开始的{range_cm}cm范围内K(i)值均为2。")
                return "III类桩", report_details

        # 2、II类桩判定
        if num_K2 == 1 and not has_K3 and not has_K4:
            k2_depths = [d for d, k in K_values_map_with_depths.items() if k == 2]
            report_details.append(f"所有检测截面仅存在一个K(i)=2的情况，且不存在Ki=3、Ki=4，深度为: {k2_depths[0]:.2f}m。")
            return "II类桩", report_details

        if has_K2 and num_K2 > 1 and not has_K3 and not has_K4:
            continuous_K2_found, _ = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=2, depth_range=gz_depth_range)
            if not continuous_K2_found:
                report_details.append(f"存在Ki=2的截面不止一个({num_K2}个)，且不存在Ki=3、Ki=4，且不存在某深度{range_cm}cm范围内K(i)值均为2。")
                return "II类桩", report_details

        # 回退判定
        if has_K4:
            k4_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 4])
            depth_str = "，".join([f"{d:.2f}m" for d in k4_depths])
            report_details.append(f"桩身存在K(i)=4的检测横截面，主要问题截面深度为: {depth_str}。")
            return "IV类桩", report_details
        elif has_K3:
            report_details.append("桩身存在K(i)=3的检测横截面，但不满足特定III类或IV类条件。")
            return "III类桩", report_details
        elif has_K2:
            report_details.append("桩身存在K(i)=2的检测横截面，但不满足特定II类或III类条件。")
            return "II类桩", report_details

        report_details.append("未能明确分类，或数据不满足任何明确的I-IV类桩条件。请检查K值分布。")
        return "未定类别", report_details

    def run_analysis(self):
        """
        执行GZ传统分析

        Returns:
            dict: 分析结果
        """
        print("[ANALYSIS] Starting GZ traditional analysis...")
        if self.data_df is None or self.data_df.empty:
            print("[ERROR] No data loaded")
            return None

        try:
            print(f"[DATA] Data shape: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")

            # 获取配置参数
            enabled_indicators = self.config['enabled_indicators']
            gz_depth_range = self.config['gz_depth_range']
            gz_enable_depth_range = self.config['gz_enable_depth_range']

            # 初始化结果字典
            results = {
                'I_ji_values': {},
                'K_values': {},
                'final_category': None,
                'report_details': [],
                'analysis_summary': "",
                'detailed_analysis': {},
                'config_used': self.gz_config,
                'enabled_indicators': enabled_indicators,
                'raw_data': {},
                'gz_depth_range': gz_depth_range,
                'gz_enable_depth_range': gz_enable_depth_range
            }

            # 基础必需列
            required_gz_cols = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
            # 可选的Energy和PSD列
            optional_cols = ['E1', 'E2', 'E3', 'P1', 'P2', 'P3']

            temp_df = self.data_df.copy()
            for col in required_gz_cols + optional_cols:
                if col not in temp_df.columns:
                    temp_df[col] = np.nan

            # 对每个深度进行分析
            for _, row in temp_df.iterrows():
                depth = row['Depth']
                if pd.isna(depth):
                    continue

                profiles_data = {
                    '1-2': {
                        'speed': row.get('S1', np.nan),
                        'amplitude': row.get('A1', np.nan),
                        'energy': row.get('E1', np.nan),
                        'psd': row.get('P1', np.nan)
                    },
                    '1-3': {
                        'speed': row.get('S2', np.nan),
                        'amplitude': row.get('A2', np.nan),
                        'energy': row.get('E2', np.nan),
                        'psd': row.get('P2', np.nan)
                    },
                    '2-3': {
                        'speed': row.get('S3', np.nan),
                        'amplitude': row.get('A3', np.nan),
                        'energy': row.get('E3', np.nan),
                        'psd': row.get('P3', np.nan)
                    }
                }

                K_values_at_depth = {}
                # 存储该深度的原始数据
                if depth not in results['raw_data']:
                    results['raw_data'][depth] = {}

                for profile, data in profiles_data.items():
                    # 存储原始数据
                    results['raw_data'][depth][profile] = data.copy()

                    # 检查是否有任何启用的指标具有有效数据
                    has_basic_data = (enabled_indicators['speed'] and pd.notnull(data['speed'])) or \
                                   (enabled_indicators['amplitude'] and pd.notnull(data['amplitude'])) or \
                                   (enabled_indicators['energy'] and pd.notnull(data['energy'])) or \
                                   (enabled_indicators['psd'] and pd.notnull(data['psd']))

                    if has_basic_data:
                        # 使用增强的I(j,i)计算函数
                        I_ji_value = self.calculate_I_ji_enhanced(
                            Sp=data['speed'] if enabled_indicators['speed'] and pd.notnull(data['speed']) else None,
                            Ad=data['amplitude'] if enabled_indicators['amplitude'] and pd.notnull(data['amplitude']) else None,
                            Energy=data['energy'] if enabled_indicators['energy'] and pd.notnull(data['energy']) else None,
                            PSD=data['psd'] if enabled_indicators['psd'] and pd.notnull(data['psd']) else None,
                            enabled_indicators=enabled_indicators
                        )
                        K_values_at_depth[profile] = I_ji_value

                if K_values_at_depth:
                    # 存储各剖面的I(j,i)值
                    results['I_ji_values'][depth] = K_values_at_depth

            # K值计算
            self._calculate_K_values(results)

            # 桩类判定
            final_category, report_details = self.determine_final_category(results['K_values'], gz_depth_range)
            results['final_category'] = final_category
            results['report_details'] = report_details
            results['analysis_summary'] = self._generate_analysis_summary(results)

            self.results = results
            print(f"[SUCCESS] GZ traditional analysis result: {results.get('final_category', 'N/A')}")
            return results

        except Exception as e:
            print(f"[ERROR] GZ traditional analysis error: {str(e)}")
            traceback.print_exc()
            return None

    def _calculate_K_values(self, results):
        """计算K值"""
        gz_enable_depth_range = results['gz_enable_depth_range']
        gz_depth_range = results['gz_depth_range']

        if gz_enable_depth_range:
            # 启用深度范围：使用深度范围计算K值
            print(f"[DEBUG] 启用深度范围模式，使用深度范围 {gz_depth_range}m 计算K值")
            for depth in results['I_ji_values'].keys():
                # 收集深度范围内的所有I(j,i)值
                I_ji_values_in_range = []
                for d, i_ji_dict in results['I_ji_values'].items():
                    if abs(d - depth) <= gz_depth_range / 2.0:  # 深度范围的一半作为半径
                        I_ji_values_in_range.extend(i_ji_dict.values())

                if I_ji_values_in_range:
                    # 使用深度范围内的所有I(j,i)值计算K值
                    K_i = self.calculate_K_i(I_ji_values_in_range)
                    results['K_values'][depth] = K_i
                    print(f"[DEBUG] Depth {depth:.2f}m: 深度范围±{gz_depth_range/2.0:.2f}m内I(j,i)值 = {I_ji_values_in_range}, calculated K(i) = {K_i}")
                else:
                    # 如果范围内没有数据，使用当前深度的I(j,i)值
                    I_ji_list = list(results['I_ji_values'][depth].values())
                    K_i = self.calculate_K_i(I_ji_list)
                    results['K_values'][depth] = K_i
                    print(f"[DEBUG] Depth {depth:.2f}m: 仅使用当前深度I(j,i)值 = {I_ji_list}, calculated K(i) = {K_i}")
        else:
            # 未启用深度范围：仅使用当前深度的I(j,i)值计算K值
            print(f"[DEBUG] 未启用深度范围模式，仅使用当前深度I(j,i)值计算K值")
            for depth in results['I_ji_values'].keys():
                I_ji_list = list(results['I_ji_values'][depth].values())
                K_i = self.calculate_K_i(I_ji_list)
                results['K_values'][depth] = K_i
                print(f"[DEBUG] Depth {depth:.2f}m: 当前深度I(j,i)值 = {I_ji_list}, calculated K(i) = {K_i}")

    def _generate_analysis_summary(self, results):
        """生成分析摘要"""
        gz_depth_range = results.get('gz_depth_range', 0.5)
        gz_enable_depth_range = results.get('gz_enable_depth_range', True)
        depth_range_cm = int(gz_depth_range * 100)

        summary = f"GZ方法桩基完整性分析结果\n" + "=" * 40 + "\n\n" + f"最终判定: {results['final_category']}\n\n"

        summary += f"分析配置:\n"
        if gz_enable_depth_range:
            summary += f"- K值计算深度范围: 已启用 ({gz_depth_range}m / {depth_range_cm}cm)\n"
        else:
            summary += f"- K值计算深度范围: 未启用 (仅使用当前深度点)\n"

        # 显示启用的指标
        enabled_indicators = results.get('enabled_indicators', {})
        enabled_list = []
        if enabled_indicators.get('speed', False):
            enabled_list.append('声速')
        if enabled_indicators.get('amplitude', False):
            enabled_list.append('波幅')
        if enabled_indicators.get('energy', False):
            enabled_list.append('能量')
        if enabled_indicators.get('psd', False):
            enabled_list.append('PSD')
        summary += f"- 启用指标: {', '.join(enabled_list)}\n"

        # 添加判定依据说明
        summary += f"- 判定依据: 新版GZ Traditional Analysis分类规则\n\n"

        summary += f"新版分类规则说明:\n"
        summary += f"I类桩: 所有检测截面Ki值均为1\n"
        summary += f"II类桩: 仅存在一个K=2，或多个K=2但不存在{depth_range_cm}cm连续范围\n"
        summary += f"III类桩: 仅存在一个K=3，或多个K=3且距离≥{depth_range_cm}cm，或K=2存在{depth_range_cm}cm连续范围\n"
        summary += f"IV类桩: 仅存在一个K=4，或K=3存在{depth_range_cm}cm连续范围\n\n"

        summary += f"判定依据:\n"
        for detail in results['report_details']:
            summary += f"- {detail}\n"
        summary += f"\nK值分布统计:\n"
        if results['K_values']:
            k_counts = {}
            for k_val in results['K_values'].values():
                k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]
                percentage = (count / len(results['K_values'])) * 100
                summary += f"K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        summary += f"\n总计分析截面: {len(results['K_values'])}个\n"
        return summary

    def update_config(self, new_config):
        """更新配置"""
        if new_config:
            self.config.update(new_config)
            print(f"[CONFIG] Configuration updated: {new_config}")

    def get_results(self):
        """获取分析结果"""
        return self.results

    def get_I_ji_reason(self, I_ji_value, data, enabled_indicators=None):
        """根据I(j,i)值和原始数据，返回取值原因说明"""
        try:
            if enabled_indicators is None:
                enabled_indicators = self.config['enabled_indicators']

            speed = data.get('speed', None)
            amplitude = data.get('amplitude', None)
            energy = data.get('energy', None)
            psd = data.get('psd', None)

            reasons = []

            # 根据I(j,i)值分析原因，只显示启用的指标
            if I_ji_value == 1:
                if enabled_indicators.get('speed', False) and speed is not None and 100.0 <= speed <= 1000.0:
                    reasons.append(f"声速{speed:.1f}%≥100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and -100 <= amplitude <= 3:
                    reasons.append(f"波幅{amplitude:.1f}dB≤3dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.8 <= energy <= 100:
                    reasons.append(f"能量{energy:.2f}≥0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 0 <= psd <= 1:
                    reasons.append(f"PSD{psd:.2f}≤1")

            elif I_ji_value == 2:
                if enabled_indicators.get('speed', False) and speed is not None and 85.0 <= speed < 100.0:
                    reasons.append(f"声速{speed:.1f}%在85-100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 3 < amplitude <= 6:
                    reasons.append(f"波幅{amplitude:.1f}dB在3-6dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.5 <= energy < 0.8:
                    reasons.append(f"能量{energy:.2f}在0.5-0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 1 < psd <= 2:
                    reasons.append(f"PSD{psd:.2f}在1-2")

            elif I_ji_value == 3:
                if enabled_indicators.get('speed', False) and speed is not None and 75.0 <= speed < 85.0:
                    reasons.append(f"声速{speed:.1f}%在75-85%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 6 < amplitude <= 12:
                    reasons.append(f"波幅{amplitude:.1f}dB在6-12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.25 <= energy < 0.5:
                    reasons.append(f"能量{energy:.2f}在0.25-0.5")
                if enabled_indicators.get('psd', False) and psd is not None and 2 < psd <= 3:
                    reasons.append(f"PSD{psd:.2f}在2-3")

            elif I_ji_value == 4:
                if enabled_indicators.get('speed', False) and speed is not None and 65.0 <= speed < 75.0:
                    reasons.append(f"声速{speed:.1f}%在65-75%")
                elif enabled_indicators.get('speed', False) and speed is not None and speed < 65.0:
                    reasons.append(f"声速{speed:.1f}%<65%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and amplitude > 12:
                    reasons.append(f"波幅{amplitude:.1f}dB>12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0 <= energy < 0.25:
                    reasons.append(f"能量{energy:.2f}<0.25")
                if enabled_indicators.get('psd', False) and psd is not None and psd > 3:
                    reasons.append(f"PSD{psd:.2f}>3")

            if reasons:
                return "，".join(reasons)
            else:
                # 如果没有找到具体原因，显示启用指标的原始数据
                data_parts = []
                if enabled_indicators.get('speed', False) and speed is not None:
                    data_parts.append(f"声速{speed:.1f}%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None:
                    data_parts.append(f"波幅{amplitude:.1f}dB")
                if enabled_indicators.get('energy', False) and energy is not None:
                    data_parts.append(f"能量{energy:.2f}")
                if enabled_indicators.get('psd', False) and psd is not None:
                    data_parts.append(f"PSD{psd:.2f}")
                return "，".join(data_parts) if data_parts else "数据不完整"

        except Exception as e:
            print(f"[ERROR] 获取I(j,i)原因失败: {e}")
            return "计算原因获取失败"

    def generate_detailed_report(self):
        """生成详细报告"""
        if self.results is None:
            return "没有分析结果可显示。\n"

        result = self.results
        report = f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n"

        summary = result.get('analysis_summary', '')
        if summary:
            report += summary + "\n"

        report += "详细分析结果:\n" + "-" * 50 + "\n"

        K_values = result.get('K_values', {})
        I_ji_values = result.get('I_ji_values', {})
        raw_data = result.get('raw_data', {})

        for depth in sorted(K_values.keys()):
            report += f"深度 {depth:.2f}m: K(i) = {K_values[depth]}\n"

            if depth in I_ji_values:
                # 显示各剖面的I(j,i)值和计算原因
                for profile, I_ji in I_ji_values[depth].items():
                    report += f"  剖面{profile}: I(j,i) = {I_ji}"

                    # 添加I(j,i)取值原因说明
                    if depth in raw_data and profile in raw_data[depth]:
                        data = raw_data[depth][profile]
                        enabled_indicators = result.get('enabled_indicators', {})
                        reason = self.get_I_ji_reason(I_ji, data, enabled_indicators)
                        report += f" ({reason})"

                    report += "\n"

                # 添加K值计算过程
                I_ji_list = list(I_ji_values[depth].values())
                if len(I_ji_list) > 0:
                    report += f"  K值计算过程:\n"
                    report += f"    公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n"

                    # 计算详细步骤
                    sum_squares = sum(i**2 for i in I_ji_list)
                    sum_values = sum(I_ji_list)

                    report += f"    I(j,i)值: {I_ji_list}\n"
                    report += f"    ∑I(j,i)² = {' + '.join([f'{i}²' for i in I_ji_list])} = {' + '.join([str(i**2) for i in I_ji_list])} = {sum_squares}\n"
                    report += f"    ∑I(j,i) = {' + '.join([str(i) for i in I_ji_list])} = {sum_values}\n"

                    if sum_values > 0:
                        ratio = sum_squares / sum_values
                        final_value = ratio + 0.5
                        k_result = int(final_value)
                        report += f"    计算: ({sum_squares} / {sum_values}) + 0.5 = {ratio:.2f} + 0.5 = {final_value:.2f}\n"
                        report += f"    取整: int({final_value:.2f}) = {k_result}\n"
                    else:
                        report += f"    注意: ∑I(j,i) = 0，无法计算K值\n"

            report += "\n"

        return report

class GZAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GZ Traditional Analysis - 桩基完整性分析")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.analyzer = None
        self.current_file = None
        self.current_folder = None
        self.selected_files = []
        self.results = None
        self.batch_results = {}
        
        # Create GUI components
        self.create_widgets()
        self.setup_layout()
        
        # Set default values
        self.set_default_values()
        
    def create_widgets(self):
        """创建GUI组件"""
        
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # File selection frame
        self.file_frame = ttk.LabelFrame(self.main_frame, text="批量数据文件选择", padding="10")

        # Folder selection
        self.folder_frame = ttk.Frame(self.file_frame)
        self.folder_path_var = tk.StringVar()
        self.folder_entry = ttk.Entry(self.folder_frame, textvariable=self.folder_path_var, width=50)
        self.browse_folder_button = ttk.Button(self.folder_frame, text="选择文件夹", command=self.browse_folder)
        self.scan_button = ttk.Button(self.folder_frame, text="扫描文件", command=self.scan_files)

        # File list
        self.file_list_frame = ttk.Frame(self.file_frame)
        ttk.Label(self.file_list_frame, text="找到的 *_with_energy.txt 文件:").pack(anchor="w")

        # File listbox with scrollbar
        self.file_listbox_frame = ttk.Frame(self.file_list_frame)
        self.file_listbox = tk.Listbox(self.file_listbox_frame, height=6, selectmode=tk.EXTENDED)
        self.file_scrollbar = ttk.Scrollbar(self.file_listbox_frame, orient="vertical", command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=self.file_scrollbar.set)

        # File list control buttons
        self.file_control_frame = ttk.Frame(self.file_list_frame)
        self.select_all_button = ttk.Button(self.file_control_frame, text="全选", command=self.select_all_files)
        self.deselect_all_button = ttk.Button(self.file_control_frame, text="全不选", command=self.deselect_all_files)
        self.load_button = ttk.Button(self.file_control_frame, text="加载选中文件", command=self.load_selected_files)
        
        # Configuration frame
        self.config_frame = ttk.LabelFrame(self.main_frame, text="分析配置", padding="10")
        
        # Indicators configuration
        self.indicators_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.indicators_frame, text="启用指标:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.speed_var = tk.BooleanVar(value=True)
        self.amplitude_var = tk.BooleanVar(value=True)
        self.energy_var = tk.BooleanVar(value=True)
        self.psd_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(self.indicators_frame, text="声速", variable=self.speed_var).grid(row=0, column=1, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="波幅", variable=self.amplitude_var).grid(row=0, column=2, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="能量", variable=self.energy_var).grid(row=0, column=3, padx=5)
        ttk.Checkbutton(self.indicators_frame, text="PSD", variable=self.psd_var).grid(row=0, column=4, padx=5)
        
        # Depth range configuration
        self.depth_frame = ttk.Frame(self.config_frame)
        ttk.Label(self.depth_frame, text="深度范围(m):").grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.depth_range_var = tk.DoubleVar(value=0.5)
        self.depth_spinbox = ttk.Spinbox(self.depth_frame, from_=0.1, to=2.0, increment=0.1, 
                                        textvariable=self.depth_range_var, width=10)
        self.depth_spinbox.grid(row=0, column=1, padx=5)
        
        self.enable_depth_range_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(self.depth_frame, text="启用深度范围", 
                       variable=self.enable_depth_range_var).grid(row=0, column=2, padx=10)
        
        # Control buttons frame
        self.control_frame = ttk.Frame(self.main_frame)

        self.analyze_button = ttk.Button(self.control_frame, text="开始批量分析",
                                        command=self.start_batch_analysis, state="disabled")
        self.save_button = ttk.Button(self.control_frame, text="保存批量结果",
                                     command=self.save_batch_results, state="disabled")
        self.clear_button = ttk.Button(self.control_frame, text="清除结果", command=self.clear_results)
        self.config_button = ttk.Button(self.control_frame, text="加载配置", command=self.load_config)
        self.save_config_button = ttk.Button(self.control_frame, text="保存配置", command=self.save_config)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(self.control_frame, textvariable=self.progress_var)
        self.progress_bar = ttk.Progressbar(self.control_frame, mode='determinate')

        # Batch progress info
        self.batch_progress_var = tk.StringVar(value="")
        self.batch_progress_label = ttk.Label(self.control_frame, textvariable=self.batch_progress_var)
        
        # Results frame
        self.results_frame = ttk.LabelFrame(self.main_frame, text="批量分析结果", padding="10")

        # Batch results summary
        self.summary_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.summary_frame, text="已分析文件数:").grid(row=0, column=0, sticky="w", padx=(0, 10))

        self.analyzed_files_var = tk.StringVar(value="0")
        self.analyzed_files_label = ttk.Label(self.summary_frame, textvariable=self.analyzed_files_var,
                                             font=("Arial", 12, "bold"))
        self.analyzed_files_label.grid(row=0, column=1, sticky="w")

        ttk.Label(self.summary_frame, text="总截面数:").grid(row=0, column=2, sticky="w", padx=(20, 10))
        self.total_sections_var = tk.StringVar(value="0")
        ttk.Label(self.summary_frame, textvariable=self.total_sections_var).grid(row=0, column=3, sticky="w")
        
        # Batch category distribution
        self.category_dist_frame = ttk.Frame(self.results_frame)
        ttk.Label(self.category_dist_frame, text="桩类分布:").grid(row=0, column=0, sticky="nw", padx=(0, 10))

        self.category_dist_var = tk.StringVar(value="")
        self.category_dist_label = ttk.Label(self.category_dist_frame, textvariable=self.category_dist_var, justify="left")
        self.category_dist_label.grid(row=0, column=1, sticky="w")
        
        # Detailed results text area
        self.text_frame = ttk.Frame(self.results_frame)
        self.results_text = scrolledtext.ScrolledText(self.text_frame, height=15, width=80, 
                                                     wrap=tk.WORD, font=("Consolas", 9))
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        
    def setup_layout(self):
        """设置布局"""
        
        # Main frame
        self.main_frame.pack(fill="both", expand=True)
        
        # File selection frame
        self.file_frame.pack(fill="x", pady=(0, 10))

        # Folder selection layout
        self.folder_frame.pack(fill="x", pady=(0, 5))
        self.folder_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        self.browse_folder_button.pack(side="right", padx=(0, 5))
        self.scan_button.pack(side="right")

        # File list layout
        self.file_list_frame.pack(fill="both", expand=True)
        self.file_listbox_frame.pack(fill="both", expand=True, pady=(5, 5))
        self.file_listbox.pack(side="left", fill="both", expand=True)
        self.file_scrollbar.pack(side="right", fill="y")

        # File control buttons layout
        self.file_control_frame.pack(fill="x")
        self.select_all_button.pack(side="left", padx=(0, 5))
        self.deselect_all_button.pack(side="left", padx=(0, 5))
        self.load_button.pack(side="right")
        
        # Configuration frame
        self.config_frame.pack(fill="x", pady=(0, 10))
        self.indicators_frame.pack(fill="x", pady=(0, 5))
        self.depth_frame.pack(fill="x")
        
        # Control frame
        self.control_frame.pack(fill="x", pady=(0, 10))
        self.analyze_button.pack(side="left", padx=(0, 5))
        self.save_button.pack(side="left", padx=(0, 5))
        self.clear_button.pack(side="left", padx=(0, 5))
        self.config_button.pack(side="left", padx=(0, 5))
        self.save_config_button.pack(side="left", padx=(0, 20))
        self.progress_label.pack(side="left", padx=(0, 10))
        self.batch_progress_label.pack(side="left", padx=(0, 10))
        self.progress_bar.pack(side="left", fill="x", expand=True)
        
        # Results frame
        self.results_frame.pack(fill="both", expand=True)
        self.summary_frame.pack(fill="x", pady=(0, 5))
        self.category_dist_frame.pack(fill="x", pady=(0, 5))
        self.text_frame.pack(fill="both", expand=True)
        self.results_text.pack(fill="both", expand=True)
        
        # Status bar
        self.status_frame.pack(fill="x", side="bottom")
        self.status_label.pack(fill="x")
        
    def set_default_values(self):
        """设置默认值"""
        self.update_status("就绪 - 请选择包含 *_with_energy.txt 文件的文件夹")

    def browse_folder(self):
        """浏览文件夹"""
        folder_path = filedialog.askdirectory(
            title="选择包含 *_with_energy.txt 文件的文件夹",
            initialdir=os.getcwd()
        )

        if folder_path:
            self.folder_path_var.set(folder_path)
            self.current_folder = folder_path
            self.update_status(f"已选择文件夹: {os.path.basename(folder_path)}")
            # 自动扫描文件
            self.scan_files()

    def scan_files(self):
        """扫描文件夹中的 *_with_energy.txt 文件"""
        if not self.current_folder or not os.path.exists(self.current_folder):
            messagebox.showerror("错误", "请先选择有效的文件夹")
            return

        try:
            # 搜索符合条件的文件
            pattern = os.path.join(self.current_folder, "*_with_energy.txt")
            found_files = glob.glob(pattern)

            # 清空文件列表
            self.file_listbox.delete(0, tk.END)
            self.selected_files = []

            if found_files:
                # 按文件名排序
                found_files.sort()

                # 添加到列表框
                for file_path in found_files:
                    filename = os.path.basename(file_path)
                    self.file_listbox.insert(tk.END, filename)
                    self.selected_files.append(file_path)

                # 默认全选
                self.select_all_files()

                self.update_status(f"找到 {len(found_files)} 个 *_with_energy.txt 文件")
            else:
                self.update_status("未找到 *_with_energy.txt 文件")
                messagebox.showinfo("提示", f"在文件夹 {self.current_folder} 中未找到 *_with_energy.txt 文件")

        except Exception as e:
            messagebox.showerror("错误", f"扫描文件时出错: {str(e)}")

    def select_all_files(self):
        """全选文件"""
        self.file_listbox.select_set(0, tk.END)

    def deselect_all_files(self):
        """全不选文件"""
        self.file_listbox.selection_clear(0, tk.END)

    def load_selected_files(self):
        """加载选中的文件"""
        selected_indices = self.file_listbox.curselection()

        if not selected_indices:
            messagebox.showerror("错误", "请至少选择一个文件")
            return

        # 获取选中的文件路径
        self.selected_files = []
        for index in selected_indices:
            filename = self.file_listbox.get(index)
            file_path = os.path.join(self.current_folder, filename)
            self.selected_files.append(file_path)

        self.update_status(f"已选择 {len(self.selected_files)} 个文件进行分析")
        self.analyze_button.config(state="normal")

        # 显示选中文件的预览
        preview = f"选中的文件 ({len(self.selected_files)} 个):\n\n"
        for i, file_path in enumerate(self.selected_files, 1):
            filename = os.path.basename(file_path)
            preview += f"{i}. {filename}\n"

        preview += f"\n准备进行批量分析..."

        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, preview)

    def browse_file(self):
        """浏览文件"""
        file_types = [
            ("CSV files", "*.csv"),
            ("TSV files", "*.tsv"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )
        
        if filename:
            self.file_path_var.set(filename)
            self.current_file = filename
            self.update_status(f"已选择文件: {os.path.basename(filename)}")
            
    def load_data(self):
        """加载数据"""
        if not self.current_file or not os.path.exists(self.current_file):
            messagebox.showerror("错误", "请先选择有效的数据文件")
            return
            
        try:
            self.update_status("正在加载数据...")
            self.progress_var.set("加载中...")
            self.progress_bar.start()
            
            # Create analyzer and load data
            self.analyzer = GZTraditionalAnalyzer()
            success = self.analyzer.load_data_from_file(self.current_file)
            
            self.progress_bar.stop()
            
            if success:
                data_shape = self.analyzer.data_df.shape
                self.update_status(f"数据加载成功: {data_shape[0]} 行, {data_shape[1]} 列")
                self.progress_var.set("数据已加载")
                self.analyze_button.config(state="normal")
                
                # Show data preview
                preview = f"数据预览:\n"
                preview += f"文件: {os.path.basename(self.current_file)}\n"
                preview += f"数据形状: {data_shape}\n"
                preview += f"列名: {list(self.analyzer.data_df.columns)}\n\n"
                preview += "前5行数据:\n"
                preview += str(self.analyzer.data_df.head())
                
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, preview)
                
            else:
                self.update_status("数据加载失败")
                self.progress_var.set("加载失败")
                messagebox.showerror("错误", "数据加载失败，请检查文件格式")
                
        except Exception as e:
            self.progress_bar.stop()
            self.update_status("数据加载出错")
            self.progress_var.set("出错")
            messagebox.showerror("错误", f"数据加载出错: {str(e)}")
            
    def start_batch_analysis(self):
        """开始批量分析"""
        if not self.selected_files:
            messagebox.showerror("错误", "请先选择要分析的文件")
            return

        # Run batch analysis in a separate thread to avoid blocking GUI
        thread = threading.Thread(target=self.run_batch_analysis)
        thread.daemon = True
        thread.start()

    def start_analysis(self):
        """开始单文件分析（保留兼容性）"""
        if not self.analyzer or self.analyzer.data_df is None:
            messagebox.showerror("错误", "请先加载数据")
            return

        # Run analysis in a separate thread to avoid blocking GUI
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_batch_analysis(self):
        """运行批量分析（在后台线程中）"""
        try:
            # Update GUI in main thread
            self.root.after(0, self.update_batch_analysis_start)

            # 清空之前的批量结果
            self.batch_results = {}
            total_files = len(self.selected_files)

            # 获取分析配置
            config = {
                'enabled_indicators': {
                    'speed': self.speed_var.get(),
                    'amplitude': self.amplitude_var.get(),
                    'energy': self.energy_var.get(),
                    'psd': self.psd_var.get()
                },
                'gz_depth_range': self.depth_range_var.get(),
                'gz_enable_depth_range': self.enable_depth_range_var.get()
            }

            # 逐个分析文件
            for i, file_path in enumerate(self.selected_files):
                filename = os.path.basename(file_path)

                # 更新进度
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress, f=filename, idx=i+1, total=total_files:
                               self.update_batch_progress(p, f, idx, total))

                try:
                    # 创建新的分析器实例
                    analyzer = GZTraditionalAnalyzer()
                    analyzer.update_config(config)

                    # 加载数据
                    if analyzer.load_data_from_file(file_path):
                        # 运行分析
                        result = analyzer.run_analysis()
                        if result:
                            self.batch_results[filename] = {
                                'file_path': file_path,
                                'result': result,
                                'analyzer': analyzer,
                                'status': 'success'
                            }
                        else:
                            self.batch_results[filename] = {
                                'file_path': file_path,
                                'result': None,
                                'analyzer': None,
                                'status': 'analysis_failed',
                                'error': '分析失败'
                            }
                    else:
                        self.batch_results[filename] = {
                            'file_path': file_path,
                            'result': None,
                            'analyzer': None,
                            'status': 'load_failed',
                            'error': '数据加载失败'
                        }

                except Exception as e:
                    self.batch_results[filename] = {
                        'file_path': file_path,
                        'result': None,
                        'analyzer': None,
                        'status': 'error',
                        'error': str(e)
                    }

            # 完成进度更新
            self.root.after(0, lambda: self.update_batch_progress(100, "完成", total_files, total_files))

            # Update GUI with results in main thread
            self.root.after(0, self.update_batch_analysis_complete)

        except Exception as e:
            # Handle error in main thread
            self.root.after(0, lambda: self.update_batch_analysis_error(str(e)))

    def run_analysis(self):
        """运行单文件分析（在后台线程中）"""
        try:
            # Update GUI in main thread
            self.root.after(0, self.update_analysis_start)

            # Update analyzer configuration
            config = {
                'enabled_indicators': {
                    'speed': self.speed_var.get(),
                    'amplitude': self.amplitude_var.get(),
                    'energy': self.energy_var.get(),
                    'psd': self.psd_var.get()
                },
                'gz_depth_range': self.depth_range_var.get(),
                'gz_enable_depth_range': self.enable_depth_range_var.get()
            }

            self.analyzer.update_config(config)

            # Run analysis
            self.results = self.analyzer.run_analysis()

            # Update GUI with results in main thread
            self.root.after(0, self.update_analysis_complete)

        except Exception as e:
            # Handle error in main thread
            self.root.after(0, lambda: self.update_analysis_error(str(e)))
            
    def update_batch_analysis_start(self):
        """更新批量分析开始状态"""
        self.update_status("正在进行批量GZ传统分析...")
        self.progress_var.set("批量分析中...")
        self.batch_progress_var.set("准备中...")
        self.progress_bar.config(mode='determinate')
        self.progress_bar['value'] = 0
        self.analyze_button.config(state="disabled")

    def update_batch_progress(self, progress, current_file, current_index, total_files):
        """更新批量分析进度"""
        self.progress_bar['value'] = progress
        self.batch_progress_var.set(f"({current_index}/{total_files}) {current_file}")

    def update_batch_analysis_complete(self):
        """更新批量分析完成状态"""
        self.progress_bar['value'] = 100

        if self.batch_results:
            # 统计结果
            total_files = len(self.batch_results)
            successful_files = sum(1 for r in self.batch_results.values() if r['status'] == 'success')

            # 统计桩类分布
            category_counts = {}
            total_sections = 0

            for filename, result_data in self.batch_results.items():
                if result_data['status'] == 'success' and result_data['result']:
                    category = result_data['result'].get('final_category', '未知')
                    category_counts[category] = category_counts.get(category, 0) + 1

                    # 统计截面数
                    k_values = result_data['result'].get('K_values', {})
                    total_sections += len(k_values)

            # 更新汇总信息
            self.analyzed_files_var.set(f"{successful_files}/{total_files}")
            self.total_sections_var.set(str(total_sections))

            # 更新桩类分布
            if category_counts:
                category_dist_text = ""
                for category in sorted(category_counts.keys()):
                    count = category_counts[category]
                    percentage = (count / successful_files) * 100 if successful_files > 0 else 0
                    category_dist_text += f"{category}: {count}个 ({percentage:.1f}%)  "
                self.category_dist_var.set(category_dist_text)

            # 生成详细报告
            detailed_report = self.generate_batch_report()
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, detailed_report)

            self.update_status(f"批量分析完成 - 成功分析 {successful_files}/{total_files} 个文件")
            self.progress_var.set("批量分析完成")
            self.batch_progress_var.set("完成")
            self.save_button.config(state="normal")

        else:
            self.update_status("批量分析失败")
            self.progress_var.set("批量分析失败")
            self.batch_progress_var.set("失败")
            messagebox.showerror("错误", "批量分析失败，请检查文件和配置")

        self.analyze_button.config(state="normal")

    def update_batch_analysis_error(self, error_msg):
        """更新批量分析错误状态"""
        self.progress_bar['value'] = 0
        self.update_status("批量分析出错")
        self.progress_var.set("出错")
        self.batch_progress_var.set("出错")
        self.analyze_button.config(state="normal")
        messagebox.showerror("批量分析错误", f"批量分析过程中出错: {error_msg}")

    def update_analysis_start(self):
        """更新单文件分析开始状态"""
        self.update_status("正在进行GZ传统分析...")
        self.progress_var.set("分析中...")
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()
        self.analyze_button.config(state="disabled")
        
    def update_analysis_complete(self):
        """更新分析完成状态"""
        self.progress_bar.stop()
        
        if self.results:
            # Update summary
            category = self.results.get('final_category', '未知')
            self.category_var.set(category)
            
            # Set category color
            if category == "I类桩":
                self.category_label.config(foreground="green")
            elif category == "II类桩":
                self.category_label.config(foreground="blue")
            elif category == "III类桩":
                self.category_label.config(foreground="orange")
            elif category == "IV类桩":
                self.category_label.config(foreground="red")
            else:
                self.category_label.config(foreground="black")
                
            sections_count = len(self.results.get('K_values', {}))
            self.sections_var.set(str(sections_count))
            
            # Update K-value distribution
            k_values = self.results.get('K_values', {})
            if k_values:
                k_counts = {}
                for k_val in k_values.values():
                    k_counts[k_val] = k_counts.get(k_val, 0) + 1
                
                k_dist_text = ""
                for k_val in sorted(k_counts.keys()):
                    count = k_counts[k_val]
                    percentage = (count / len(k_values)) * 100
                    k_dist_text += f"K={k_val}: {count}个 ({percentage:.1f}%)  "
                
                self.k_dist_var.set(k_dist_text)
            
            # Show detailed results
            detailed_report = self.analyzer.generate_detailed_report()
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, detailed_report)
            
            self.update_status(f"分析完成 - 结果: {category}")
            self.progress_var.set("分析完成")
            self.save_button.config(state="normal")
            
        else:
            self.update_status("分析失败")
            self.progress_var.set("分析失败")
            messagebox.showerror("错误", "分析失败，请检查数据和配置")
            
        self.analyze_button.config(state="normal")

    def generate_batch_report(self):
        """生成批量分析报告"""
        if not self.batch_results:
            return "无批量分析结果"

        report = "=" * 80 + "\n"
        report += "桩基完整性GZ传统分析批量检测报告\n"
        report += "=" * 80 + "\n\n"

        # 报告基本信息
        report += f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
        report += f"分析方法: GZ传统分析法\n"
        report += f"分析软件: 桩基完整性分析系统 v2.0\n\n"

        # 桩身完整性判定方法与步骤说明
        report += "一、桩身完整性判定方法与步骤\n"
        report += "-" * 50 + "\n\n"

        report += "1. 数据预处理阶段:\n"
        report += "   - 加载和解析桩基检测数据文件\n"
        report += "   - 标准化数据列名和格式\n"
        report += "   - 处理缺失值和异常数据\n\n"

        report += "2. I(j,i)值计算阶段:\n"
        report += "   对每个深度的每个剖面（1-2, 1-3, 2-3）计算完整性指标I(j,i)值:\n"
        report += "   - I(j,i)=1 (正常): 声速≥100%, 波幅≤3dB, 能量≥0.8, PSD≤1\n"
        report += "   - I(j,i)=2 (轻微畸变): 声速85-100%, 波幅3-6dB, 能量0.5-0.8, PSD1-2\n"
        report += "   - I(j,i)=3 (明显畸变): 声速75-85%, 波幅6-12dB, 能量0.25-0.5, PSD2-3\n"
        report += "   - I(j,i)=4 (严重畸变): 声速65-75%, 波幅>12dB, 能量<0.25, PSD>3\n\n"

        report += "3. K(i)值计算阶段:\n"
        report += "   使用公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n"
        report += "   - 对每个深度截面的所有剖面I(j,i)值进行综合计算\n"
        report += "   - 可选择启用深度范围模式进行区域性评估\n\n"

        report += "4. 桩类判定阶段:\n"
        report += "   根据K值分布进行桩身完整性分类:\n"
        report += "   - I类桩: 所有检测截面K值均为1，桩身完整\n"
        report += "   - II类桩: 存在个别K=2截面，桩身基本完整\n"
        report += "   - III类桩: 存在K=3截面或K=2连续分布，桩身有明显缺陷\n"
        report += "   - IV类桩: 存在K=4截面或K=3连续分布，桩身有严重缺陷\n\n"

        # 汇总信息
        total_files = len(self.batch_results)
        successful_files = sum(1 for r in self.batch_results.values() if r['status'] == 'success')
        failed_files = total_files - successful_files

        report += "二、批量检测汇总信息\n"
        report += "-" * 50 + "\n\n"
        report += f"检测文件总数: {total_files}\n"
        report += f"成功分析: {successful_files}\n"
        report += f"分析失败: {failed_files}\n"
        report += f"成功率: {(successful_files/total_files)*100:.1f}%\n\n"

        # 桩类统计
        category_counts = {}
        total_sections = 0
        for result_data in self.batch_results.values():
            if result_data['status'] == 'success' and result_data['result']:
                category = result_data['result'].get('final_category', '未知')
                category_counts[category] = category_counts.get(category, 0) + 1
                k_values = result_data['result'].get('K_values', {})
                total_sections += len(k_values)

        if category_counts:
            report += "三、桩身完整性分类统计\n"
            report += "-" * 50 + "\n\n"
            report += f"总检测截面数: {total_sections}个\n"
            report += f"总检测桩数: {successful_files}根\n\n"

            report += "桩类分布详情:\n"
            for category in ['I类桩', 'II类桩', 'III类桩', 'IV类桩']:
                if category in category_counts:
                    count = category_counts[category]
                    percentage = (count / successful_files) * 100 if successful_files > 0 else 0
                    report += f"  {category}: {count}根 ({percentage:.1f}%)\n"

                    # 添加桩类说明
                    if category == 'I类桩':
                        report += f"    - 桩身完整，质量良好\n"
                    elif category == 'II类桩':
                        report += f"    - 桩身基本完整，有轻微缺陷\n"
                    elif category == 'III类桩':
                        report += f"    - 桩身有明显缺陷，需要注意\n"
                    elif category == 'IV类桩':
                        report += f"    - 桩身有严重缺陷，存在质量问题\n"

            # 添加其他类别
            for category in sorted(category_counts.keys()):
                if category not in ['I类桩', 'II类桩', 'III类桩', 'IV类桩']:
                    count = category_counts[category]
                    percentage = (count / successful_files) * 100 if successful_files > 0 else 0
                    report += f"  {category}: {count}根 ({percentage:.1f}%)\n"
            report += "\n"

        # 详细结果
        report += "四、各桩详细分析结果\n"
        report += "-" * 80 + "\n"

        pile_index = 1
        for filename, result_data in self.batch_results.items():
            report += f"\n桩号 {pile_index}: {filename}\n"
            report += "-" * 60 + "\n"

            if result_data['status'] == 'success':
                result = result_data['result']
                category = result.get('final_category', '未知')
                k_values = result.get('K_values', {})
                sections_count = len(k_values)
                report_details = result.get('report_details', [])
                enabled_indicators = result.get('enabled_indicators', {})

                report += f"分析状态: 成功\n"
                report += f"桩类判定: {category}\n"
                report += f"检测截面数: {sections_count}个\n"

                # 显示启用的分析指标
                enabled_list = []
                if enabled_indicators.get('speed', False):
                    enabled_list.append('声速')
                if enabled_indicators.get('amplitude', False):
                    enabled_list.append('波幅')
                if enabled_indicators.get('energy', False):
                    enabled_list.append('能量')
                if enabled_indicators.get('psd', False):
                    enabled_list.append('PSD')
                report += f"分析指标: {', '.join(enabled_list)}\n"

                # K值分布统计
                if k_values:
                    k_counts = {}
                    for k_val in k_values.values():
                        k_counts[k_val] = k_counts.get(k_val, 0) + 1

                    report += f"K值分布统计:\n"
                    for k_val in sorted(k_counts.keys()):
                        count = k_counts[k_val]
                        percentage = (count / sections_count) * 100
                        report += f"  K={k_val}: {count}个截面 ({percentage:.1f}%)\n"

                # 判定依据
                report += f"判定依据:\n"
                for detail in report_details:
                    report += f"  - {detail}\n"

                # 深度范围内的关键截面信息
                if k_values:
                    problem_sections = []
                    for depth, k_val in k_values.items():
                        if k_val >= 3:  # K=3或K=4的问题截面
                            problem_sections.append((depth, k_val))

                    if problem_sections:
                        report += f"关键问题截面:\n"
                        for depth, k_val in sorted(problem_sections):
                            report += f"  深度{depth:.2f}m: K={k_val}\n"
                    else:
                        report += f"无严重问题截面\n"

            else:
                report += f"分析状态: 失败\n"
                if 'error' in result_data:
                    report += f"错误信息: {result_data['error']}\n"

            pile_index += 1

        # 技术说明和建议
        report += "\n五、技术说明与建议\n"
        report += "-" * 50 + "\n\n"

        report += "1. 分析方法说明:\n"
        report += "   本报告采用GZ传统分析法，基于超声波检测数据进行桩身完整性评估。\n"
        report += "   该方法通过分析声速、波幅、能量等多个指标的综合表现来判定桩身质量。\n\n"

        report += "2. 结果解释:\n"
        report += "   - I类桩: 桩身完整，承载力满足设计要求\n"
        report += "   - II类桩: 桩身基本完整，承载力基本满足设计要求\n"
        report += "   - III类桩: 桩身有明显缺陷，承载力可能降低，建议进一步检测\n"
        report += "   - IV类桩: 桩身有严重缺陷，承载力明显不足，需要处理或重新施工\n\n"

        report += "3. 质量评估建议:\n"
        if category_counts:
            # 根据桩类分布给出建议
            total_piles = successful_files
            problem_piles = category_counts.get('III类桩', 0) + category_counts.get('IV类桩', 0)
            good_piles = category_counts.get('I类桩', 0) + category_counts.get('II类桩', 0)

            if problem_piles == 0:
                report += "   - 所有检测桩质量良好，工程质量符合要求\n"
            elif problem_piles / total_piles <= 0.1:
                report += "   - 大部分桩质量良好，个别问题桩需要重点关注\n"
                report += "   - 建议对III类和IV类桩进行补充检测或加固处理\n"
            elif problem_piles / total_piles <= 0.3:
                report += "   - 存在一定比例的问题桩，需要加强质量控制\n"
                report += "   - 建议全面检查施工工艺和材料质量\n"
            else:
                report += "   - 问题桩比例较高，工程质量存在较大风险\n"
                report += "   - 建议暂停施工，全面排查质量问题\n"

        report += "\n4. 注意事项:\n"
        report += "   - 本分析结果仅基于提供的检测数据，实际工程应用需结合现场情况\n"
        report += "   - 对于III类和IV类桩，建议进行钻芯取样等补充检测\n"
        report += "   - 检测结果的准确性依赖于原始数据的质量和完整性\n\n"

        report += "=" * 80 + "\n"
        report += f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n"
        report += f"分析软件: 桩基完整性分析系统 v2.0\n"
        report += f"技术支持: GZ传统分析算法\n"
        report += "=" * 80 + "\n"

        return report

    def update_analysis_error(self, error_msg):
        """更新分析错误状态"""
        self.progress_bar.stop()
        self.update_status("分析出错")
        self.progress_var.set("出错")
        self.analyze_button.config(state="normal")
        messagebox.showerror("分析错误", f"分析过程中出错: {error_msg}")
        
    def save_batch_results(self):
        """保存批量分析结果"""
        if not self.batch_results:
            messagebox.showerror("错误", "没有可保存的批量分析结果")
            return

        file_types = [
            ("Text files", "*.txt"),
            ("JSON files", "*.json"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]

        # Generate default filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"batch_gz_analysis_results_{timestamp}.txt"

        filename = filedialog.asksaveasfilename(
            title="保存批量分析结果",
            defaultextension=".txt",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                # Determine format from extension
                ext = os.path.splitext(filename)[1].lower()

                if ext == '.json':
                    self.save_batch_results_json(filename)
                elif ext == '.csv':
                    self.save_batch_results_csv(filename)
                else:
                    self.save_batch_results_txt(filename)

                self.update_status(f"批量结果已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"批量分析结果已保存到: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存批量结果时出错: {str(e)}")

    def save_batch_results_txt(self, filename):
        """保存批量结果为TXT格式"""
        report = self.generate_batch_report()
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

    def save_batch_results_json(self, filename):
        """保存批量结果为JSON格式"""
        json_data = {
            'analysis_time': datetime.now().isoformat(),
            'total_files': len(self.batch_results),
            'successful_files': sum(1 for r in self.batch_results.values() if r['status'] == 'success'),
            'results': {}
        }

        for filename_key, result_data in self.batch_results.items():
            json_data['results'][filename_key] = {
                'file_path': result_data['file_path'],
                'status': result_data['status'],
                'result': result_data['result'] if result_data['status'] == 'success' else None,
                'error': result_data.get('error', None)
            }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

    def save_batch_results_csv(self, filename):
        """保存批量结果为CSV格式"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入标题行
            writer.writerow(['文件名', '状态', '桩类判定', '截面数', 'K值分布', '错误信息'])

            # 写入数据行
            for filename_key, result_data in self.batch_results.items():
                if result_data['status'] == 'success' and result_data['result']:
                    result = result_data['result']
                    category = result.get('final_category', '未知')
                    k_values = result.get('K_values', {})
                    sections_count = len(k_values)

                    # 计算K值分布
                    k_counts = {}
                    for k_val in k_values.values():
                        k_counts[k_val] = k_counts.get(k_val, 0) + 1
                    k_dist = ", ".join([f"K={k}({count})" for k, count in sorted(k_counts.items())])

                    writer.writerow([filename_key, '成功', category, sections_count, k_dist, ''])
                else:
                    error_msg = result_data.get('error', '未知错误')
                    writer.writerow([filename_key, '失败', '', '', '', error_msg])

    def save_results(self):
        """保存单文件结果（保留兼容性）"""
        if not self.results:
            messagebox.showerror("错误", "没有可保存的结果")
            return

        file_types = [
            ("Text files", "*.txt"),
            ("JSON files", "*.json"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]

        # Generate default filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_analysis_results_{timestamp}.txt"

        filename = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".txt",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                # Determine format from extension
                ext = os.path.splitext(filename)[1].lower()
                if ext == '.json':
                    format_type = 'json'
                elif ext == '.csv':
                    format_type = 'csv'
                else:
                    format_type = 'txt'

                success = self.analyzer.save_results(filename, format_type)

                if success:
                    self.update_status(f"结果已保存: {os.path.basename(filename)}")
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存结果失败")

            except Exception as e:
                messagebox.showerror("错误", f"保存结果时出错: {str(e)}")
                
    def clear_results(self):
        """清除结果"""
        self.results_text.delete(1.0, tk.END)

        # 清除单文件结果显示
        self.category_var.set("未分析")
        self.sections_var.set("0")

        # 清除批量结果显示
        self.analyzed_files_var.set("0")
        self.total_sections_var.set("0")
        self.category_dist_var.set("")

        self.category_label.config(foreground="black")
        self.progress_var.set("就绪")
        self.batch_progress_var.set("")
        self.save_button.config(state="disabled")
        self.analyze_button.config(state="disabled")

        # 重置进度条
        self.progress_bar['value'] = 0

        # 清除数据
        self.results = None
        self.batch_results = {}
        self.selected_files = []

        # 清空文件列表
        self.file_listbox.delete(0, tk.END)

        self.update_status("结果已清除")
        
    def load_config(self):
        """加载配置文件"""
        file_types = [("JSON files", "*.json"), ("All files", "*.*")]

        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=file_types,
            initialdir=os.getcwd()
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Update GUI with loaded configuration
                enabled_indicators = config.get('enabled_indicators', {})
                self.speed_var.set(enabled_indicators.get('speed', True))
                self.amplitude_var.set(enabled_indicators.get('amplitude', True))
                self.energy_var.set(enabled_indicators.get('energy', True))
                self.psd_var.set(enabled_indicators.get('psd', False))

                self.depth_range_var.set(config.get('gz_depth_range', 0.5))
                self.enable_depth_range_var.set(config.get('gz_enable_depth_range', True))

                self.update_status(f"配置已加载: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已加载: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        config = {
            'enabled_indicators': {
                'speed': self.speed_var.get(),
                'amplitude': self.amplitude_var.get(),
                'energy': self.energy_var.get(),
                'psd': self.psd_var.get()
            },
            'gz_depth_range': self.depth_range_var.get(),
            'gz_enable_depth_range': self.enable_depth_range_var.get(),
            'saved_time': datetime.now().isoformat(),
            'description': 'GZ Traditional Analysis Configuration'
        }

        file_types = [("JSON files", "*.json"), ("All files", "*.*")]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f"gz_config_{timestamp}.json"

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            initialname=default_name,
            filetypes=file_types
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                self.update_status(f"配置已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"配置文件已保存: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(f"{datetime.now().strftime('%H:%M:%S')} - {message}")


def main():
    """主函数"""
    root = tk.Tk()
    GZAnalysisGUI(root)
    
    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')  # You can add an icon file
    except:
        pass
    
    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
