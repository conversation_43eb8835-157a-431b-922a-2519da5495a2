#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GZ Traditional Analysis - Independent Script
GZ传统分析独立脚本

This script provides standalone GZ traditional analysis functionality including:
- Data loading and parsing
- GZ traditional analysis calculations
- Configuration management for GZ method
- Result reporting and export

Extracted from Pile_analyze_GZ_gui_INT_S2.py for modular use.

Author: Pile Integrity Analysis System
Version: 1.0
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime
import traceback
import argparse

# --- GZ Method Core Configuration ---
DEFAULT_GZ_CONFIG = {
    'Sp_conditions': {
        'ge_100': lambda sp: sp >= 100, '85_lt_100': lambda sp: 85 <= sp < 100,
        '75_lt_85': lambda sp: 75 <= sp < 85, '65_lt_75': lambda sp: 65 <= sp < 75,
        'lt_65': lambda sp: sp < 65, 'ge_85': lambda sp: sp >= 85,
        'ge_75': lambda sp: sp >= 75, 'ge_65': lambda sp: sp >= 65,
    },
    'Ad_conditions': {
        'le_0': lambda ad: ad <= 0, 'gt_0_le_4': lambda ad: 0 < ad <= 4,
        'gt_4_le_8': lambda ad: 4 < ad <= 8, 'gt_8_le_12': lambda ad: 8 < ad <= 12,
        'gt_12': lambda ad: ad > 12, 'le_4': lambda ad: ad <= 4,
        'le_8': lambda ad: ad <= 8, 'le_12': lambda ad: ad <= 12,
    },
    'Energy_conditions': {
        'I_1': lambda e: 0.8 <= e <= 100.0,  # I(j,i)=1: 正常
        'I_2': lambda e: 0.5 <= e < 0.8,      # I(j,i)=2: 轻微畸变
        'I_3': lambda e: 0.25 <= e < 0.5,     # I(j,i)=3: 明显畸变
        'I_4': lambda e: 0.0 <= e < 0.25,     # I(j,i)=4: 严重畸变
        'normal': lambda e: 0.8 <= e <= 100.0, 'light': lambda e: 0.5 <= e < 0.8,
        'obvious': lambda e: 0.25 <= e < 0.5, 'severe': lambda e: 0.0 <= e < 0.25,
    },
    'PSD_conditions': {
        'I_1': lambda p: 0.0 <= p <= 1.0,     # I(j,i)=1: 正常
        'I_2': lambda p: 1.0 < p <= 2.0,      # I(j,i)=2: 轻微畸变
        'I_3': lambda p: 2.0 < p <= 3.0,      # I(j,i)=3: 明显畸变
        'I_4': lambda p: 3.0 < p <= 100.0,    # I(j,i)=4: 严重畸变
        'normal': lambda p: 0.0 <= p <= 1.0, 'light': lambda p: 1.0 < p <= 2.0,
        'obvious': lambda p: 2.0 < p <= 3.0, 'severe': lambda p: 3.0 < p <= 100.0,
    },
    'Bi_ratio_conditions': {
        'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8,
        'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5,
        'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25,
    }
}

# --- Default Analysis Configuration ---
DEFAULT_ANALYSIS_CONFIG = {
    'enabled_indicators': {
        'speed': True,
        'amplitude': True,
        'energy': True,
        'psd': False
    },
    'gz_depth_range': 0.5,  # 深度范围(m)
    'gz_enable_depth_range': True,  # 是否启用深度范围
    'bi_ratio_default': 1.0,  # 默认Bi比值
}

class GZTraditionalAnalyzer:
    """GZ Traditional Analysis Core Engine"""
    
    def __init__(self, config=None):
        """
        Initialize GZ Traditional Analyzer
        
        Args:
            config (dict): Analysis configuration
        """
        self.config = config or DEFAULT_ANALYSIS_CONFIG.copy()
        self.gz_config = DEFAULT_GZ_CONFIG.copy()
        self.data_df = None
        self.results = None
        
    def load_data_from_file(self, file_path):
        """
        Load and parse data from file
        
        Args:
            file_path (str): Path to data file
            
        Returns:
            bool: True if successful, False otherwise
        """
        print(f"[FILE] Loading data file: {file_path}")
        try:
            self.data_df = self._parse_data_file(file_path)
            if self.data_df is None or self.data_df.empty:
                print("[ERROR] Data loading failed or file is empty")
                return False
            print(f"[SUCCESS] Data loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            return True
        except Exception as e:
            print(f"[ERROR] Data loading error: {str(e)}")
            traceback.print_exc()
            return False
    
    def load_data_from_dataframe(self, df):
        """
        Load data from pandas DataFrame
        
        Args:
            df (pd.DataFrame): Input data
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.data_df = self._standardize_dataframe(df.copy())
            if self.data_df is None or self.data_df.empty:
                print("[ERROR] DataFrame is empty or invalid")
                return False
            print(f"[SUCCESS] DataFrame loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            return True
        except Exception as e:
            print(f"[ERROR] DataFrame loading error: {str(e)}")
            traceback.print_exc()
            return False
    
    def _parse_data_file(self, file_path):
        """Parse data file and standardize column names"""
        try:
            # Try different separators
            separators = ['\t', ',', ';', ' ']
            df = None
            
            for sep in separators:
                try:
                    df = pd.read_csv(file_path, sep=sep, header=0)
                    if len(df.columns) > 1:  # Successfully parsed
                        break
                except:
                    continue
            
            if df is None or len(df.columns) <= 1:
                raise ValueError("Unable to parse file with any separator")
            
            print(f"原始数据列名: {list(df.columns)}, 数据形状: {df.shape}")
            return self._standardize_dataframe(df)
            
        except Exception as e:
            print(f"数据解析错误: {str(e)}")
            traceback.print_exc()
            return None
    
    def _standardize_dataframe(self, df):
        """Standardize DataFrame column names and data types"""
        try:
            # Column mapping for standardization
            column_mapping = {}
            for col_name in df.columns:
                col_lower = col_name.lower().replace('_', '').replace(' ', '').replace('%', '')
                if 'depth' in col_lower: 
                    column_mapping[col_name] = 'Depth'
                elif '1-2speed' in col_lower or '12speed' in col_lower: 
                    column_mapping[col_name] = 'S1'
                elif '1-2amp' in col_lower or '12amp' in col_lower: 
                    column_mapping[col_name] = 'A1'
                elif '1-3speed' in col_lower or '13speed' in col_lower: 
                    column_mapping[col_name] = 'S2'
                elif '1-3amp' in col_lower or '13amp' in col_lower: 
                    column_mapping[col_name] = 'A2'
                elif '2-3speed' in col_lower or '23speed' in col_lower: 
                    column_mapping[col_name] = 'S3'
                elif '2-3amp' in col_lower or '23amp' in col_lower: 
                    column_mapping[col_name] = 'A3'
                # Energy% columns
                elif '1-2energy' in col_lower or '12energy' in col_lower: 
                    column_mapping[col_name] = 'E1'
                elif '1-3energy' in col_lower or '13energy' in col_lower: 
                    column_mapping[col_name] = 'E2'
                elif '2-3energy' in col_lower or '23energy' in col_lower: 
                    column_mapping[col_name] = 'E3'
                # PSD columns
                elif '1-2psd' in col_lower or '12psd' in col_lower: 
                    column_mapping[col_name] = 'P1'
                elif '1-3psd' in col_lower or '13psd' in col_lower: 
                    column_mapping[col_name] = 'P2'
                elif '2-3psd' in col_lower or '23psd' in col_lower: 
                    column_mapping[col_name] = 'P3'

            df.rename(columns=column_mapping, inplace=True)
            print(f"重命名后列名: {list(df.columns)}")
            
            # Ensure required columns exist
            required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告：缺少列 {missing_columns}")
            
            # Add missing columns with NaN values
            for col in required_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                else:
                    df[col] = np.nan
                    print(f"Added missing column '{col}' with NaNs.")

            # Remove rows with missing depth values
            original_len = len(df)
            df.dropna(subset=['Depth'], inplace=True)
            print(f"处理缺失值后: {len(df)} 行 (原始: {original_len} 行)")
            
            return df
            
        except Exception as e:
            print(f"数据标准化错误: {str(e)}")
            traceback.print_exc()
            return None

    def calculate_I_ji_enhanced(self, Sp, Ad, Energy=None, PSD=None, enabled_indicators=None):
        """
        增强的I(j,i)计算函数，按照GZ Traditional Analysis公式
        I(j,i)=1：Speed%(100.0-1000.0)，Amp%(-100-3)，Energy%(0.8-100), PSD(0-1)
        I(j,i)=2：Speed%(85.0-100.0)，Amp%(3-6)，Energy%(0.5-0.8), PSD(1-2)
        I(j,i)=3：Speed%(75.0-85.0)，Amp%(6-12)，Energy%(0.25-0.5), PSD(2-3)
        I(j,i)=4：Speed%(65.0-75.0)，Amp%(12-100)，Energy%(0-0.25), PSD(3-100)
        """
        if enabled_indicators is None:
            enabled_indicators = self.config['enabled_indicators']

        # 计算各指标的I(j,i)值
        i_values = []

        # Speed% 分类
        if enabled_indicators.get('speed', True) and Sp is not None:
            if 100.0 <= Sp <= 1000.0:
                i_values.append(1)  # I(j,i)=1
            elif 85.0 <= Sp < 100.0:
                i_values.append(2)  # I(j,i)=2
            elif 75.0 <= Sp < 85.0:
                i_values.append(3)  # I(j,i)=3
            elif 65.0 <= Sp < 75.0:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # Amplitude 分类
        if enabled_indicators.get('amplitude', True) and Ad is not None:
            if -100 <= Ad <= 3:
                i_values.append(1)  # I(j,i)=1
            elif 3 < Ad <= 6:
                i_values.append(2)  # I(j,i)=2
            elif 6 < Ad <= 12:
                i_values.append(3)  # I(j,i)=3
            elif 12 < Ad <= 100:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # Energy% 分类
        if enabled_indicators.get('energy', False) and Energy is not None:
            if 0.8 <= Energy <= 100:
                i_values.append(1)  # I(j,i)=1
            elif 0.5 <= Energy < 0.8:
                i_values.append(2)  # I(j,i)=2
            elif 0.25 <= Energy < 0.5:
                i_values.append(3)  # I(j,i)=3
            elif 0 <= Energy < 0.25:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # PSD 分类
        if enabled_indicators.get('psd', False) and PSD is not None:
            if 0 <= PSD <= 1:
                i_values.append(1)  # I(j,i)=1
            elif 1 < PSD <= 2:
                i_values.append(2)  # I(j,i)=2
            elif 2 < PSD <= 3:
                i_values.append(3)  # I(j,i)=3
            elif 3 < PSD <= 100:
                i_values.append(4)  # I(j,i)=4
            else:
                i_values.append(4)  # 超出范围，视为严重畸变

        # 如果没有启用任何指标，返回1（正常）
        if not i_values:
            return 1

        # 取最严重的分类作为最终I(j,i)值
        return max(i_values)

    def calculate_K_i(self, I_ji_values_at_depth):
        """计算K(i)值"""
        if not I_ji_values_at_depth:
            return 0
        valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
        if not valid_I_ji:
            return 0
        sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)
        sum_I_ji = sum(valid_I_ji)
        if sum_I_ji == 0:
            return 0
        K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
        return int(K_i_float)

    def check_continuous_K_in_range(self, K_values_with_depths, target_K, depth_range=0.5, depth_interval=0.1):
        """
        检查是否存在某深度范围内K(i)值均为target_K的情况
        """
        if not K_values_with_depths:
            return False, -1

        sorted_k_data = sorted(K_values_with_depths.items())
        required_points = int(depth_range / depth_interval) + 1
        range_span = depth_range

        for i in range(len(sorted_k_data) - required_points + 1):
            start_depth = sorted_k_data[i][0]
            continuous_found = True
            depths_in_range = []

            for j in range(required_points):
                expected_depth = start_depth + j * depth_interval
                found_match = False
                for depth, k_val in sorted_k_data:
                    if abs(depth - expected_depth) <= depth_interval * 0.1:
                        if k_val == target_K:
                            depths_in_range.append(depth)
                            found_match = True
                            break
                        else:
                            continuous_found = False
                            break

                if not found_match:
                    continuous_found = False
                    break

            if continuous_found and len(depths_in_range) == required_points:
                actual_span = max(depths_in_range) - min(depths_in_range)
                if abs(actual_span - range_span) <= depth_interval:
                    return True, min(depths_in_range)

        return False, -1

    def determine_final_category(self, K_values_map_with_depths, gz_depth_range=0.5):
        """
        根据新的判定依据确定桩类
        """
        report_details = []
        if not K_values_map_with_depths:
            return "N/A", ["没有计算K值。"]

        K_values_list = list(K_values_map_with_depths.values())
        has_K4 = any(k == 4 for k in K_values_list)
        has_K3 = any(k == 3 for k in K_values_list)
        has_K2 = any(k == 2 for k in K_values_list)

        num_K4 = K_values_list.count(4)
        num_K3 = K_values_list.count(3)
        num_K2 = K_values_list.count(2)

        range_cm = int(gz_depth_range * 100)
        print(f"[DEBUG] K值统计: K=1:{K_values_list.count(1)}, K=2:{num_K2}, K=3:{num_K3}, K=4:{num_K4}")

        # 1、I类桩：所有检测截面Ki值均为1
        if all(k == 1 for k in K_values_list):
            report_details.append("所有检测截面Ki值均为1。")
            return "I类桩", report_details

        # 4、IV类桩判定
        if num_K4 == 1:
            k4_depths = [d for d, k in K_values_map_with_depths.items() if k == 4]
            report_details.append(f"所有检测截面仅存在一个K(i)=4的情况，深度为: {k4_depths[0]:.2f}m。")
            return "IV类桩", report_details

        if has_K3 and num_K3 > 1 and not has_K4:
            continuous_K3_found, k3_start_depth = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=3, depth_range=gz_depth_range)
            if continuous_K3_found:
                report_details.append(f"存在Ki=3的截面不止一个，且不存在Ki=4，且在深度{k3_start_depth:.2f}m开始的{range_cm}cm范围内K(i)值均为3。")
                return "IV类桩", report_details

        # 3、III类桩判定
        if num_K3 == 1 and not has_K4:
            k3_depths = [d for d, k in K_values_map_with_depths.items() if k == 3]
            report_details.append(f"所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4，深度为: {k3_depths[0]:.2f}m。")
            return "III类桩", report_details

        if has_K3 and num_K3 > 1 and not has_K4:
            k3_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 3])
            all_k3_separated = True
            for i in range(len(k3_depths) - 1):
                distance = k3_depths[i+1] - k3_depths[i]
                if distance < gz_depth_range:
                    all_k3_separated = False
                    break

            if all_k3_separated:
                report_details.append(f"存在Ki=3的截面不止一个({num_K3}个)，且不存在Ki=4，且任意两个相邻的Ki=3截面距离≥{range_cm}cm。")
                return "III类桩", report_details

        if has_K2 and num_K2 > 1 and not has_K3 and not has_K4:
            continuous_K2_found, k2_start_depth = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=2, depth_range=gz_depth_range)
            if continuous_K2_found:
                report_details.append(f"存在Ki=2的截面不止一个，且不存在Ki=3、Ki=4，且在深度{k2_start_depth:.2f}m开始的{range_cm}cm范围内K(i)值均为2。")
                return "III类桩", report_details

        # 2、II类桩判定
        if num_K2 == 1 and not has_K3 and not has_K4:
            k2_depths = [d for d, k in K_values_map_with_depths.items() if k == 2]
            report_details.append(f"所有检测截面仅存在一个K(i)=2的情况，且不存在Ki=3、Ki=4，深度为: {k2_depths[0]:.2f}m。")
            return "II类桩", report_details

        if has_K2 and num_K2 > 1 and not has_K3 and not has_K4:
            continuous_K2_found, _ = self.check_continuous_K_in_range(K_values_map_with_depths, target_K=2, depth_range=gz_depth_range)
            if not continuous_K2_found:
                report_details.append(f"存在Ki=2的截面不止一个({num_K2}个)，且不存在Ki=3、Ki=4，且不存在某深度{range_cm}cm范围内K(i)值均为2。")
                return "II类桩", report_details

        # 回退判定
        if has_K4:
            k4_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 4])
            depth_str = "，".join([f"{d:.2f}m" for d in k4_depths])
            report_details.append(f"桩身存在K(i)=4的检测横截面，主要问题截面深度为: {depth_str}。")
            return "IV类桩", report_details
        elif has_K3:
            report_details.append("桩身存在K(i)=3的检测横截面，但不满足特定III类或IV类条件。")
            return "III类桩", report_details
        elif has_K2:
            report_details.append("桩身存在K(i)=2的检测横截面，但不满足特定II类或III类条件。")
            return "II类桩", report_details

        report_details.append("未能明确分类，或数据不满足任何明确的I-IV类桩条件。请检查K值分布。")
        return "未定类别", report_details

    def run_analysis(self):
        """
        执行GZ传统分析

        Returns:
            dict: 分析结果
        """
        print("[ANALYSIS] Starting GZ traditional analysis...")
        if self.data_df is None or self.data_df.empty:
            print("[ERROR] No data loaded")
            return None

        try:
            print(f"[DATA] Data shape: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")

            # 获取配置参数
            enabled_indicators = self.config['enabled_indicators']
            gz_depth_range = self.config['gz_depth_range']
            gz_enable_depth_range = self.config['gz_enable_depth_range']

            # 初始化结果字典
            results = {
                'I_ji_values': {},
                'K_values': {},
                'final_category': None,
                'report_details': [],
                'analysis_summary': "",
                'detailed_analysis': {},
                'config_used': self.gz_config,
                'enabled_indicators': enabled_indicators,
                'raw_data': {},
                'gz_depth_range': gz_depth_range,
                'gz_enable_depth_range': gz_enable_depth_range
            }

            # 基础必需列
            required_gz_cols = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
            # 可选的Energy和PSD列
            optional_cols = ['E1', 'E2', 'E3', 'P1', 'P2', 'P3']

            temp_df = self.data_df.copy()
            for col in required_gz_cols + optional_cols:
                if col not in temp_df.columns:
                    temp_df[col] = np.nan

            # 对每个深度进行分析
            for _, row in temp_df.iterrows():
                depth = row['Depth']
                if pd.isna(depth):
                    continue

                profiles_data = {
                    '1-2': {
                        'speed': row.get('S1', np.nan),
                        'amplitude': row.get('A1', np.nan),
                        'energy': row.get('E1', np.nan),
                        'psd': row.get('P1', np.nan)
                    },
                    '1-3': {
                        'speed': row.get('S2', np.nan),
                        'amplitude': row.get('A2', np.nan),
                        'energy': row.get('E2', np.nan),
                        'psd': row.get('P2', np.nan)
                    },
                    '2-3': {
                        'speed': row.get('S3', np.nan),
                        'amplitude': row.get('A3', np.nan),
                        'energy': row.get('E3', np.nan),
                        'psd': row.get('P3', np.nan)
                    }
                }

                K_values_at_depth = {}
                # 存储该深度的原始数据
                if depth not in results['raw_data']:
                    results['raw_data'][depth] = {}

                for profile, data in profiles_data.items():
                    # 存储原始数据
                    results['raw_data'][depth][profile] = data.copy()

                    # 检查是否有任何启用的指标具有有效数据
                    has_basic_data = (enabled_indicators['speed'] and pd.notnull(data['speed'])) or \
                                   (enabled_indicators['amplitude'] and pd.notnull(data['amplitude'])) or \
                                   (enabled_indicators['energy'] and pd.notnull(data['energy'])) or \
                                   (enabled_indicators['psd'] and pd.notnull(data['psd']))

                    if has_basic_data:
                        # 使用增强的I(j,i)计算函数
                        I_ji_value = self.calculate_I_ji_enhanced(
                            Sp=data['speed'] if enabled_indicators['speed'] and pd.notnull(data['speed']) else None,
                            Ad=data['amplitude'] if enabled_indicators['amplitude'] and pd.notnull(data['amplitude']) else None,
                            Energy=data['energy'] if enabled_indicators['energy'] and pd.notnull(data['energy']) else None,
                            PSD=data['psd'] if enabled_indicators['psd'] and pd.notnull(data['psd']) else None,
                            enabled_indicators=enabled_indicators
                        )
                        K_values_at_depth[profile] = I_ji_value

                if K_values_at_depth:
                    # 存储各剖面的I(j,i)值
                    results['I_ji_values'][depth] = K_values_at_depth

            # K值计算
            self._calculate_K_values(results)

            # 桩类判定
            final_category, report_details = self.determine_final_category(results['K_values'], gz_depth_range)
            results['final_category'] = final_category
            results['report_details'] = report_details
            results['analysis_summary'] = self._generate_analysis_summary(results)

            self.results = results
            print(f"[SUCCESS] GZ traditional analysis result: {results.get('final_category', 'N/A')}")
            return results

        except Exception as e:
            print(f"[ERROR] GZ traditional analysis error: {str(e)}")
            traceback.print_exc()
            return None

    def _calculate_K_values(self, results):
        """计算K值"""
        gz_enable_depth_range = results['gz_enable_depth_range']
        gz_depth_range = results['gz_depth_range']

        if gz_enable_depth_range:
            # 启用深度范围：使用深度范围计算K值
            print(f"[DEBUG] 启用深度范围模式，使用深度范围 {gz_depth_range}m 计算K值")
            for depth in results['I_ji_values'].keys():
                # 收集深度范围内的所有I(j,i)值
                I_ji_values_in_range = []
                for d, i_ji_dict in results['I_ji_values'].items():
                    if abs(d - depth) <= gz_depth_range / 2.0:  # 深度范围的一半作为半径
                        I_ji_values_in_range.extend(i_ji_dict.values())

                if I_ji_values_in_range:
                    # 使用深度范围内的所有I(j,i)值计算K值
                    K_i = self.calculate_K_i(I_ji_values_in_range)
                    results['K_values'][depth] = K_i
                    print(f"[DEBUG] Depth {depth:.2f}m: 深度范围±{gz_depth_range/2.0:.2f}m内I(j,i)值 = {I_ji_values_in_range}, calculated K(i) = {K_i}")
                else:
                    # 如果范围内没有数据，使用当前深度的I(j,i)值
                    I_ji_list = list(results['I_ji_values'][depth].values())
                    K_i = self.calculate_K_i(I_ji_list)
                    results['K_values'][depth] = K_i
                    print(f"[DEBUG] Depth {depth:.2f}m: 仅使用当前深度I(j,i)值 = {I_ji_list}, calculated K(i) = {K_i}")
        else:
            # 未启用深度范围：仅使用当前深度的I(j,i)值计算K值
            print(f"[DEBUG] 未启用深度范围模式，仅使用当前深度I(j,i)值计算K值")
            for depth in results['I_ji_values'].keys():
                I_ji_list = list(results['I_ji_values'][depth].values())
                K_i = self.calculate_K_i(I_ji_list)
                results['K_values'][depth] = K_i
                print(f"[DEBUG] Depth {depth:.2f}m: 当前深度I(j,i)值 = {I_ji_list}, calculated K(i) = {K_i}")

    def _generate_analysis_summary(self, results):
        """生成分析摘要"""
        gz_depth_range = results.get('gz_depth_range', 0.5)
        gz_enable_depth_range = results.get('gz_enable_depth_range', True)
        depth_range_cm = int(gz_depth_range * 100)

        summary = f"GZ方法桩基完整性分析结果\n" + "=" * 40 + "\n\n" + f"最终判定: {results['final_category']}\n\n"

        summary += f"分析配置:\n"
        if gz_enable_depth_range:
            summary += f"- K值计算深度范围: 已启用 ({gz_depth_range}m / {depth_range_cm}cm)\n"
        else:
            summary += f"- K值计算深度范围: 未启用 (仅使用当前深度点)\n"

        # 显示启用的指标
        enabled_indicators = results.get('enabled_indicators', {})
        enabled_list = []
        if enabled_indicators.get('speed', False):
            enabled_list.append('声速')
        if enabled_indicators.get('amplitude', False):
            enabled_list.append('波幅')
        if enabled_indicators.get('energy', False):
            enabled_list.append('能量')
        if enabled_indicators.get('psd', False):
            enabled_list.append('PSD')
        summary += f"- 启用指标: {', '.join(enabled_list)}\n"

        # 添加判定依据说明
        summary += f"- 判定依据: 新版GZ Traditional Analysis分类规则\n\n"

        summary += f"新版分类规则说明:\n"
        summary += f"I类桩: 所有检测截面Ki值均为1\n"
        summary += f"II类桩: 仅存在一个K=2，或多个K=2但不存在{depth_range_cm}cm连续范围\n"
        summary += f"III类桩: 仅存在一个K=3，或多个K=3且距离≥{depth_range_cm}cm，或K=2存在{depth_range_cm}cm连续范围\n"
        summary += f"IV类桩: 仅存在一个K=4，或K=3存在{depth_range_cm}cm连续范围\n\n"

        summary += f"判定依据:\n"
        for detail in results['report_details']:
            summary += f"- {detail}\n"
        summary += f"\nK值分布统计:\n"
        if results['K_values']:
            k_counts = {}
            for k_val in results['K_values'].values():
                k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]
                percentage = (count / len(results['K_values'])) * 100
                summary += f"K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        summary += f"\n总计分析截面: {len(results['K_values'])}个\n"
        return summary

    def get_I_ji_reason(self, I_ji_value, data, enabled_indicators=None):
        """根据I(j,i)值和原始数据，返回取值原因说明"""
        try:
            if enabled_indicators is None:
                enabled_indicators = self.config['enabled_indicators']

            speed = data.get('speed', None)
            amplitude = data.get('amplitude', None)
            energy = data.get('energy', None)
            psd = data.get('psd', None)

            reasons = []

            # 根据I(j,i)值分析原因，只显示启用的指标
            if I_ji_value == 1:
                if enabled_indicators.get('speed', False) and speed is not None and 100.0 <= speed <= 1000.0:
                    reasons.append(f"声速{speed:.1f}%≥100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and -100 <= amplitude <= 3:
                    reasons.append(f"波幅{amplitude:.1f}dB≤3dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.8 <= energy <= 100:
                    reasons.append(f"能量{energy:.2f}≥0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 0 <= psd <= 1:
                    reasons.append(f"PSD{psd:.2f}≤1")

            elif I_ji_value == 2:
                if enabled_indicators.get('speed', False) and speed is not None and 85.0 <= speed < 100.0:
                    reasons.append(f"声速{speed:.1f}%在85-100%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 3 < amplitude <= 6:
                    reasons.append(f"波幅{amplitude:.1f}dB在3-6dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.5 <= energy < 0.8:
                    reasons.append(f"能量{energy:.2f}在0.5-0.8")
                if enabled_indicators.get('psd', False) and psd is not None and 1 < psd <= 2:
                    reasons.append(f"PSD{psd:.2f}在1-2")

            elif I_ji_value == 3:
                if enabled_indicators.get('speed', False) and speed is not None and 75.0 <= speed < 85.0:
                    reasons.append(f"声速{speed:.1f}%在75-85%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and 6 < amplitude <= 12:
                    reasons.append(f"波幅{amplitude:.1f}dB在6-12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0.25 <= energy < 0.5:
                    reasons.append(f"能量{energy:.2f}在0.25-0.5")
                if enabled_indicators.get('psd', False) and psd is not None and 2 < psd <= 3:
                    reasons.append(f"PSD{psd:.2f}在2-3")

            elif I_ji_value == 4:
                if enabled_indicators.get('speed', False) and speed is not None and 65.0 <= speed < 75.0:
                    reasons.append(f"声速{speed:.1f}%在65-75%")
                elif enabled_indicators.get('speed', False) and speed is not None and speed < 65.0:
                    reasons.append(f"声速{speed:.1f}%<65%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None and amplitude > 12:
                    reasons.append(f"波幅{amplitude:.1f}dB>12dB")
                if enabled_indicators.get('energy', False) and energy is not None and 0 <= energy < 0.25:
                    reasons.append(f"能量{energy:.2f}<0.25")
                if enabled_indicators.get('psd', False) and psd is not None and psd > 3:
                    reasons.append(f"PSD{psd:.2f}>3")

            if reasons:
                return "，".join(reasons)
            else:
                # 如果没有找到具体原因，显示启用指标的原始数据
                data_parts = []
                if enabled_indicators.get('speed', False) and speed is not None:
                    data_parts.append(f"声速{speed:.1f}%")
                if enabled_indicators.get('amplitude', False) and amplitude is not None:
                    data_parts.append(f"波幅{amplitude:.1f}dB")
                if enabled_indicators.get('energy', False) and energy is not None:
                    data_parts.append(f"能量{energy:.2f}")
                if enabled_indicators.get('psd', False) and psd is not None:
                    data_parts.append(f"PSD{psd:.2f}")
                return "，".join(data_parts) if data_parts else "数据不完整"

        except Exception as e:
            print(f"[ERROR] 获取I(j,i)原因失败: {e}")
            return "计算原因获取失败"

    def generate_detailed_report(self):
        """生成详细报告"""
        if self.results is None:
            return "没有分析结果可显示。\n"

        result = self.results
        report = f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n"

        summary = result.get('analysis_summary', '')
        if summary:
            report += summary + "\n"

        report += "详细分析结果:\n" + "-" * 50 + "\n"

        K_values = result.get('K_values', {})
        I_ji_values = result.get('I_ji_values', {})
        raw_data = result.get('raw_data', {})

        for depth in sorted(K_values.keys()):
            report += f"深度 {depth:.2f}m: K(i) = {K_values[depth]}\n"

            if depth in I_ji_values:
                # 显示各剖面的I(j,i)值和计算原因
                for profile, I_ji in I_ji_values[depth].items():
                    report += f"  剖面{profile}: I(j,i) = {I_ji}"

                    # 添加I(j,i)取值原因说明
                    if depth in raw_data and profile in raw_data[depth]:
                        data = raw_data[depth][profile]
                        enabled_indicators = result.get('enabled_indicators', {})
                        reason = self.get_I_ji_reason(I_ji, data, enabled_indicators)
                        report += f" ({reason})"

                    report += "\n"

                # 添加K值计算过程
                I_ji_list = list(I_ji_values[depth].values())
                if len(I_ji_list) > 0:
                    report += f"  K值计算过程:\n"
                    report += f"    公式: K(i) = int[(∑I(j,i)² / ∑I(j,i)) + 0.5]\n"

                    # 计算详细步骤
                    sum_squares = sum(i**2 for i in I_ji_list)
                    sum_values = sum(I_ji_list)

                    report += f"    I(j,i)值: {I_ji_list}\n"
                    report += f"    ∑I(j,i)² = {' + '.join([f'{i}²' for i in I_ji_list])} = {' + '.join([str(i**2) for i in I_ji_list])} = {sum_squares}\n"
                    report += f"    ∑I(j,i) = {' + '.join([str(i) for i in I_ji_list])} = {sum_values}\n"

                    if sum_values > 0:
                        ratio = sum_squares / sum_values
                        final_value = ratio + 0.5
                        k_result = int(final_value)
                        report += f"    计算: ({sum_squares} / {sum_values}) + 0.5 = {ratio:.2f} + 0.5 = {final_value:.2f}\n"
                        report += f"    取整: int({final_value:.2f}) = {k_result}\n"
                    else:
                        report += f"    注意: ∑I(j,i) = 0，无法计算K值\n"

            report += "\n"

        return report

    def save_results(self, output_path, format='txt'):
        """
        保存分析结果

        Args:
            output_path (str): 输出文件路径
            format (str): 输出格式 ('txt', 'json', 'csv')
        """
        if self.results is None:
            print("[ERROR] No results to save")
            return False

        try:
            if format.lower() == 'json':
                # JSON格式保存
                json_result = {}
                for key, value in self.results.items():
                    if key == 'config_used':
                        # 跳过lambda函数
                        continue
                    json_result[key] = value

                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(json_result, f, indent=2, ensure_ascii=False, default=str)

            elif format.lower() == 'csv':
                # CSV格式保存K值和I(j,i)值
                data_for_csv = []
                for depth in sorted(self.results['K_values'].keys()):
                    row = {'Depth': depth, 'K_value': self.results['K_values'][depth]}
                    if depth in self.results['I_ji_values']:
                        for profile, I_ji in self.results['I_ji_values'][depth].items():
                            row[f'I_ji_{profile}'] = I_ji
                    data_for_csv.append(row)

                df_output = pd.DataFrame(data_for_csv)
                df_output.to_csv(output_path, index=False, encoding='utf-8')

            else:
                # 默认文本格式
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write("GZ传统分析结果报告\n" + "=" * 50 + "\n\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write(self.generate_detailed_report())

            print(f"[SUCCESS] Results saved to: {output_path}")
            return True

        except Exception as e:
            print(f"[ERROR] Save results error: {str(e)}")
            traceback.print_exc()
            return False

    def update_config(self, new_config):
        """更新配置"""
        if new_config:
            self.config.update(new_config)
            print(f"[CONFIG] Configuration updated: {new_config}")

    def get_results(self):
        """获取分析结果"""
        return self.results

    def print_summary(self):
        """打印分析摘要"""
        if self.results:
            print("\n" + "="*60)
            print("GZ TRADITIONAL ANALYSIS SUMMARY")
            print("="*60)
            print(f"Final Category: {self.results.get('final_category', 'N/A')}")
            print(f"Total Sections Analyzed: {len(self.results.get('K_values', {}))}")

            # K值分布统计
            if self.results.get('K_values'):
                k_counts = {}
                for k_val in self.results['K_values'].values():
                    k_counts[k_val] = k_counts.get(k_val, 0) + 1
                print("\nK-value Distribution:")
                for k_val in sorted(k_counts.keys()):
                    count = k_counts[k_val]
                    percentage = (count / len(self.results['K_values'])) * 100
                    print(f"  K={k_val}: {count} sections ({percentage:.1f}%)")

            print("\nJudgment Details:")
            for detail in self.results.get('report_details', []):
                print(f"  - {detail}")
            print("="*60)
        else:
            print("No analysis results available.")


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='GZ Traditional Analysis - Independent Script')
    parser.add_argument('input_file', help='Input data file path')
    parser.add_argument('-o', '--output', help='Output file path (optional)')
    parser.add_argument('-f', '--format', choices=['txt', 'json', 'csv'], default='txt',
                       help='Output format (default: txt)')
    parser.add_argument('--config', help='Configuration JSON file path (optional)')
    parser.add_argument('--enable-energy', action='store_true', help='Enable energy analysis')
    parser.add_argument('--enable-psd', action='store_true', help='Enable PSD analysis')
    parser.add_argument('--depth-range', type=float, default=0.5, help='Depth range for K calculation (default: 0.5m)')

    args = parser.parse_args()

    # 创建分析器
    analyzer = GZTraditionalAnalyzer()

    # 更新配置
    config_updates = {}
    if args.enable_energy:
        config_updates['enabled_indicators'] = analyzer.config['enabled_indicators'].copy()
        config_updates['enabled_indicators']['energy'] = True
    if args.enable_psd:
        if 'enabled_indicators' not in config_updates:
            config_updates['enabled_indicators'] = analyzer.config['enabled_indicators'].copy()
        config_updates['enabled_indicators']['psd'] = True
    if args.depth_range != 0.5:
        config_updates['gz_depth_range'] = args.depth_range

    if config_updates:
        analyzer.update_config(config_updates)

    # 加载外部配置文件
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                external_config = json.load(f)
            analyzer.update_config(external_config)
            print(f"[CONFIG] Loaded external configuration from: {args.config}")
        except Exception as e:
            print(f"[WARNING] Failed to load config file: {e}")

    # 加载数据
    if not analyzer.load_data_from_file(args.input_file):
        print("[ERROR] Failed to load input data")
        return 1

    # 执行分析
    results = analyzer.run_analysis()
    if results is None:
        print("[ERROR] Analysis failed")
        return 1

    # 打印摘要
    analyzer.print_summary()

    # 保存结果
    if args.output:
        if analyzer.save_results(args.output, args.format):
            print(f"[SUCCESS] Results saved to: {args.output}")
        else:
            print("[ERROR] Failed to save results")
            return 1
    else:
        # 如果没有指定输出文件，生成默认文件名
        base_name = os.path.splitext(os.path.basename(args.input_file))[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_output = f"gz_analysis_{base_name}_{timestamp}.{args.format}"
        if analyzer.save_results(default_output, args.format):
            print(f"[SUCCESS] Results saved to: {default_output}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
