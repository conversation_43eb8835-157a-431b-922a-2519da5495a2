import pandas as pd
import numpy as np
import glob
import os

def process_single_file(input_file, output_file):
    # 原分析逻辑保持不变
    df = pd.read_excel(input_file, sheet_name='数据表', header=None)
    
    # Critical values for percentage calculations
    critical_speed_12 = df.iloc[7, 11]  # 1-2 profile speed
    critical_amp_12 = df.iloc[7, 12]    # 1-2 profile amplitude
    critical_speed_13 = df.iloc[7, 6]   # 1-3 profile speed
    critical_amp_13 = df.iloc[7, 7]     # 1-3 profile amplitude
    critical_speed_23 = df.iloc[7, 1]   # 2-3 profile speed
    critical_amp_23 = df.iloc[7, 2]     # 2-3 profile amplitude
    
    # Prepare output data
    output_lines = []
    headers = "Depth(m)\t1-2 Speed%\t1-2 Amp%\t1-2 energy%\t1-2 PSD\t1-3 Speed%\t1-3 Amp%\t1-3 energy%\t1-3 PSD\t2-3 Speed%\t2-3 Amp%\t2-3 energy%\t2-3 PSD"
    output_lines.append(headers)
    
    # Process data rows (starting from row 10)
    for i in range(9, len(df)):
        depth = df.iloc[i, 0]
        if pd.isna(depth):
            continue
            
        # Get values for each profile and convert to float
        try:
            # 1-2 profile data (columns 11, 12, 13, 15)
            speed_12 = float(df.iloc[i, 11])
            amp_12 = float(df.iloc[i, 12])
            energy_12 = df.iloc[i, 13]  # Keep original energy% value
            psd_12 = df.iloc[i, 15]     # Keep original PSD value

            # 1-3 profile data (columns 6, 7, 8, 10)
            speed_13 = float(df.iloc[i, 6])
            amp_13 = float(df.iloc[i, 7])
            energy_13 = df.iloc[i, 8]   # Keep original energy% value
            psd_13 = df.iloc[i, 10]     # Keep original PSD value

            # 2-3 profile data (columns 1, 2, 3, 5)
            speed_23 = float(df.iloc[i, 1])
            amp_23 = float(df.iloc[i, 2])
            energy_23 = df.iloc[i, 3]   # Keep original energy% value
            psd_23 = df.iloc[i, 5]      # Keep original PSD value

            # Calculate percentages for speed and amplitude
            speed_pct_12 = round((speed_12 / float(critical_speed_12)) * 100, 2)
            amp_pct_12 = round((-amp_12 + float(critical_amp_12)), 2)
            speed_pct_13 = round((speed_13 / float(critical_speed_13)) * 100, 2)
            amp_pct_13 = round((-amp_13 + float(critical_amp_13)), 2)
            speed_pct_23 = round((speed_23 / float(critical_speed_23)) * 100, 2)
            amp_pct_23 = round((-amp_23 + float(critical_amp_23)), 2)
        except (ValueError, TypeError):
            # Skip rows with invalid data
            continue
        
        # Format output line with energy% and PSD data
        line = f"{depth}\t{speed_pct_12}\t{amp_pct_12}\t{energy_12}\t{psd_12}\t{speed_pct_13}\t{amp_pct_13}\t{energy_13}\t{psd_13}\t{speed_pct_23}\t{amp_pct_23}\t{energy_23}\t{psd_23}"
        output_lines.append(line)
    
    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))

if __name__ == "__main__":
    # 遍历当前目录所有xlsx文件，排除临时文件
    for excel_file in glob.glob('*.xlsx'):
        # 跳过Excel临时文件（以~$开头）
        if excel_file.startswith('~$'):
            continue
        base_name = os.path.splitext(excel_file)[0]
        txt_file = f"{base_name}.txt"
        process_single_file(excel_file, txt_file)
        print(f"文件 {excel_file} 处理完成，结果保存至 {txt_file}")
