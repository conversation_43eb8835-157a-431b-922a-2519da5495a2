import pandas as pd
import numpy as np
import glob
import os

def process_single_file(input_file, output_file):
    # 读取Excel文件，适配新的数据结构
    df = pd.read_excel(input_file, sheet_name='数据表', header=None)

    # Critical values for percentage calculations (根据新的列结构调整)
    # 新结构：A=深度, B=1-2声速, C=1-2幅度, D=1-2_energy, F=1-3声速, G=1-3幅度, H=1-3_energy, K=2-3声速, L=2-3幅度, M=2-3_energy
    critical_speed_12 = df.iloc[7, 1]   # 1-2 profile speed (B列)
    critical_amp_12 = df.iloc[7, 2]     # 1-2 profile amplitude (C列)
    critical_speed_13 = df.iloc[7, 6]   # 1-3 profile speed (G列)
    critical_amp_13 = df.iloc[7, 7]     # 1-3 profile amplitude (H列)
    critical_speed_23 = df.iloc[7, 11]  # 2-3 profile speed (L列)
    critical_amp_23 = df.iloc[7, 12]    # 2-3 profile amplitude (M列)
    
    # Prepare output data
    output_lines = []
    headers = "Depth(m)\t1-2 Speed%\t1-2 Amp%\t1-2 energy%\t1-2 PSD\t1-3 Speed%\t1-3 Amp%\t1-3 energy%\t1-3 PSD\t2-3 Speed%\t2-3 Amp%\t2-3 energy%\t2-3 PSD"
    output_lines.append(headers)
    
    # Process data rows (starting from row 10)
    for i in range(9, len(df)):
        depth = df.iloc[i, 0]
        if pd.isna(depth):
            continue
            
        # Get values for each profile and convert to float (根据新的列结构调整)
        try:
            # 1-2 profile data (B=声速, C=幅度, D=energy%, F=PSD)
            speed_12 = float(df.iloc[i, 1])   # B列：1-2声速
            amp_12 = float(df.iloc[i, 2])     # C列：1-2幅度
            energy_12 = df.iloc[i, 3]         # D列：1-2_energy
            psd_12 = df.iloc[i, 5]            # F列：PSD

            # 1-3 profile data (G=声速, H=幅度, I=energy%, K=PSD)
            speed_13 = float(df.iloc[i, 6])   # G列：1-3声速
            amp_13 = float(df.iloc[i, 7])     # H列：1-3幅度
            energy_13 = df.iloc[i, 8]         # I列：1-3_energy
            psd_13 = df.iloc[i, 10]           # K列：PSD

            # 2-3 profile data (L=声速, M=幅度, N=energy%, P=PSD)
            speed_23 = float(df.iloc[i, 11])  # L列：2-3声速
            amp_23 = float(df.iloc[i, 12])    # M列：2-3幅度
            energy_23 = df.iloc[i, 13]        # N列：2-3_energy
            psd_23 = df.iloc[i, 15]           # P列：PSD

            # Calculate percentages for speed and amplitude
            speed_pct_12 = round((speed_12 / float(critical_speed_12)) * 100, 2)
            amp_pct_12 = round((-amp_12 + float(critical_amp_12)), 2)
            speed_pct_13 = round((speed_13 / float(critical_speed_13)) * 100, 2)
            amp_pct_13 = round((-amp_13 + float(critical_amp_13)), 2)
            speed_pct_23 = round((speed_23 / float(critical_speed_23)) * 100, 2)
            amp_pct_23 = round((-amp_23 + float(critical_amp_23)), 2)
        except (ValueError, TypeError):
            # Skip rows with invalid data
            continue
        
        # Format output line with energy% and PSD data
        line = f"{depth}\t{speed_pct_12}\t{amp_pct_12}\t{energy_12}\t{psd_12}\t{speed_pct_13}\t{amp_pct_13}\t{energy_13}\t{psd_13}\t{speed_pct_23}\t{amp_pct_23}\t{energy_23}\t{psd_23}"
        output_lines.append(line)
    
    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))

if __name__ == "__main__":
    # 只处理包含energy数据的文件（_with_energy.xlsx）
    for excel_file in glob.glob('*_with_energy.xlsx'):
        # 跳过Excel临时文件（以~$开头）
        if excel_file.startswith('~$'):
            continue
        base_name = os.path.splitext(excel_file)[0]
        txt_file = f"{base_name}.txt"
        try:
            process_single_file(excel_file, txt_file)
            print(f"文件 {excel_file} 处理完成，结果保存至 {txt_file}")
        except Exception as e:
            print(f"处理文件 {excel_file} 时出错: {e}")
