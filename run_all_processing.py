#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据处理流程脚本
减少不必要的中间文件生成
"""

import subprocess
import sys
import os
import glob

def run_script(script_name, description):
    """运行Python脚本"""
    print(f"\n{'='*50}")
    print(f"开始执行: {description}")
    print(f"脚本: {script_name}")
    print(f"{'='*50}")

    try:
        result = subprocess.run([sys.executable, script_name],
                              capture_output=True,
                              text=True,
                              encoding='utf-8')

        if result.stdout:
            print("输出:")
            print(result.stdout)

        if result.stderr:
            print("错误信息:")
            print(result.stderr)

        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False

    except Exception as e:
        print(f"❌ 执行 {script_name} 时出现异常: {e}")
        return False

def cleanup_intermediate_files():
    """清理不必要的中间文件"""
    print(f"\n{'='*50}")
    print("清理中间文件...")
    print(f"{'='*50}")

    # 要清理的文件模式
    patterns_to_clean = [
        "*_processed.xlsx",
        "*_processed_processed*.xlsx",
        "*_waveform_processed_processed*.xlsx"
    ]

    cleaned_count = 0
    for pattern in patterns_to_clean:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"删除中间文件: {file}")
                cleaned_count += 1
            except Exception as e:
                print(f"删除文件失败 {file}: {e}")

    print(f"共清理了 {cleaned_count} 个中间文件")

def main():
    """主函数"""
    print("开始简化的数据处理流程...")

    # 确保在正确的目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {script_dir}")

    # 定义处理步骤（简化版）
    steps = [
        ("waveform_data_cleaning.py", "波形数据清洗和提取"),
        ("data_fusion.py", "数据融合处理"),
        ("pile_analysis.py", "桩基分析报告生成")
    ]

    success_count = 0
    total_steps = len(steps)

    for script_name, description in steps:
        if os.path.exists(script_name):
            if run_script(script_name, description):
                success_count += 1
            else:
                print(f"\n⚠️  {description} 失败，但继续执行后续步骤...")
        else:
            print(f"\n⚠️  脚本文件不存在: {script_name}")

    # 清理中间文件
    cleanup_intermediate_files()

    # 总结
    print(f"\n{'='*60}")
    print("处理流程完成总结")
    print(f"{'='*60}")
    print(f"总步骤数: {total_steps}")
    print(f"成功步骤: {success_count}")
    print(f"失败步骤: {total_steps - success_count}")

    if success_count == total_steps:
        print("🎉 所有步骤都成功完成！")
    else:
        print("⚠️  部分步骤失败，请检查错误信息")

    print(f"\n最终生成的文件:")
    print(f"- *_waveform_processed.xlsx (波形数据处理结果)")
    print(f"- *_with_energy.xlsx (融合能量数据的完整文件)")
    print(f"- *_with_energy.txt (最终分析报告)")

if __name__ == "__main__":
    main()