import subprocess
import sys
import os

# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 要依次执行的脚本文件名
scripts = [
    'waveform_data_cleaning.py',
    'excel_data_processor_minus_mid.py',
    'data_fusion.py'
]

def run_script(script_name):
    script_path = os.path.join(script_dir, script_name)
    if not os.path.exists(script_path):
        print(f"[错误] 未找到脚本: {script_name}")
        return False
    print(f"\n[运行] {script_name} ...")
    try:
        result = subprocess.run([sys.executable, script_path], check=True)
        print(f"[完成] {script_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[失败] {script_name} 执行出错，错误码: {e.returncode}")
        return False
    except Exception as e:
        print(f"[异常] 执行 {script_name} 时发生异常: {e}")
        return False

if __name__ == "__main__":
    for script in scripts:
        success = run_script(script)
        if not success:
            print("\n[终止] 后续脚本未执行。请检查错误后重试。")
            sys.exit(1)
    print("\n[全部完成] 所有脚本已顺利执行完毕！") 