#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的GUI功能
"""

import os
import sys
import glob

def test_file_scanning():
    """测试文件扫描功能"""
    print("测试文件扫描功能...")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"当前目录: {current_dir}")
    
    # 搜索 *_with_energy.txt 文件
    pattern = os.path.join(current_dir, "*_with_energy.txt")
    found_files = glob.glob(pattern)
    
    print(f"搜索模式: {pattern}")
    print(f"找到的文件数量: {len(found_files)}")
    
    if found_files:
        print("找到的文件:")
        for i, file_path in enumerate(found_files, 1):
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"  {i}. {filename} ({file_size} bytes)")
    else:
        print("未找到任何 *_with_energy.txt 文件")
    
    return found_files

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    
    try:
        # 尝试导入修改后的GUI模块
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # 检查必要的模块
        import tkinter as tk
        print("✓ tkinter 模块导入成功")
        
        import glob
        print("✓ glob 模块导入成功")
        
        # 尝试导入GUI类（不运行）
        from gz_analysis_gui import GZAnalysisGUI
        print("✓ GZAnalysisGUI 类导入成功")
        
        # 检查关键方法是否存在
        gui_methods = [
            'browse_folder',
            'scan_files', 
            'select_all_files',
            'deselect_all_files',
            'load_selected_files',
            'start_batch_analysis',
            'run_batch_analysis',
            'generate_batch_report',
            'save_batch_results'
        ]
        
        for method_name in gui_methods:
            if hasattr(GZAnalysisGUI, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                print(f"✗ 方法 {method_name} 不存在")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修改后的GZ分析GUI测试")
    print("=" * 60)
    
    # 测试文件扫描
    found_files = test_file_scanning()
    
    # 测试GUI导入
    import_success = test_gui_import()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"找到的 *_with_energy.txt 文件: {len(found_files)} 个")
    print(f"GUI模块导入: {'成功' if import_success else '失败'}")
    
    if found_files and import_success:
        print("\n✓ 所有测试通过！GUI应该可以正常运行批量分析功能。")
        print("\n使用说明:")
        print("1. 运行 python gz_analysis_gui.py")
        print("2. 点击'选择文件夹'按钮")
        print("3. 选择包含 *_with_energy.txt 文件的文件夹")
        print("4. 系统会自动扫描并显示找到的文件")
        print("5. 选择要分析的文件，点击'加载选中文件'")
        print("6. 点击'开始批量分析'进行分析")
        print("7. 分析完成后可以保存批量结果")
    else:
        print("\n✗ 测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()