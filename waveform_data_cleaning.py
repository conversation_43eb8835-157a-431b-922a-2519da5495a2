#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波形数据清洗脚本 - 批量处理文件夹中的所有TXT文件
自动处理同一文件夹下的所有txt数据文件，为每个txt文件生成一个Excel文件
Excel文件中包含多个工作表，每个工作表对应一个构件（如1-2，1-3，2-3等）
每个工作表包含测点序号、测距和波形数据
"""

import pandas as pd
import re
import os
import glob
from typing import List, Dict

def read_file_with_encoding(file_path: str) -> str:
    """
    尝试不同编码读取文件
    """
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"成功使用 {encoding} 编码读取文件")
            return content
        except UnicodeDecodeError:
            continue
    
    raise ValueError("无法使用任何编码读取文件")

def extract_waveform_data(content: str) -> List[Dict]:
    """
    提取包含波形数据的构件信息 - 适配桩基检测数据格式
    """
    components = {}

    # 解析桩基检测数据格式
    lines = content.split('\n')

    # 查找基础数据部分
    basic_data_start = -1
    for i, line in enumerate(lines):
        if '基础数据' in line or '桩 基 名' in line:
            basic_data_start = i
            break

    if basic_data_start == -1:
        print("未找到基础数据标记")
        return []

    print("找到桩基检测数据格式")

    # 提取桩基名称
    pile_name = "unknown"
    for i in range(basic_data_start, min(basic_data_start + 20, len(lines))):
        line = lines[i].strip()
        if '桩 基 名' in line or '桩基名' in line:
            parts = line.split(':')
            if len(parts) > 1:
                pile_name = parts[1].strip()
                break

    print(f"桩基名称: {pile_name}")

    # 查找剖面数据
    profile_count = 0
    for i in range(basic_data_start, min(basic_data_start + 20, len(lines))):
        line = lines[i].strip()
        if '剖 面 数' in line or '剖面数' in line:
            parts = line.split(':')
            if len(parts) > 1:
                try:
                    profile_count = int(parts[1].strip())
                    print(f"剖面数: {profile_count}")
                except:
                    pass
            break

    # 现在处理剖面数据和波形数据
    current_profile = None
    current_point_id = None
    profile_data = {}

    # 查找剖面信息和波形数据
    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找剖面号信息
        if '剖面号:' in line:
            # 保存之前的剖面数据
            if current_profile and profile_data:
                # 只有当剖面数据不存在或者为空时才保存
                if current_profile not in components or not components[current_profile]:
                    components[current_profile] = profile_data.copy()
                    print(f"保存剖面 {current_profile}，包含 {len(profile_data)} 个测点")
                else:
                    print(f"剖面 {current_profile} 已存在，跳过保存")

            parts = line.split(':')
            if len(parts) > 1:
                profile_num = parts[1].strip()
                current_profile = f"剖面{profile_num}"
                print(f"处理剖面: {current_profile}")
                # 如果剖面已存在，使用现有数据，否则创建新的
                if current_profile in components:
                    profile_data = components[current_profile]
                    print(f"  使用现有剖面数据，包含 {len(profile_data)} 个测点")
                else:
                    profile_data = {}
                current_point_id = None

        # 查找剖面组合信息
        elif '剖面组合:' in line:
            parts = line.split(':')
            if len(parts) > 1:
                profile_combination = parts[1].strip()
                if current_profile:
                    # 保存之前的剖面数据
                    if current_profile and profile_data and current_profile not in components:
                        components[current_profile] = profile_data.copy()
                        print(f"保存剖面 {current_profile}，包含 {len(profile_data)} 个测点")

                    current_profile = profile_combination
                    print(f"剖面组合: {current_profile}")

                    # 如果剖面已存在，使用现有数据，否则创建新的
                    if current_profile in components:
                        profile_data = components[current_profile]
                        print(f"  使用现有剖面数据，包含 {len(profile_data)} 个测点")
                    else:
                        profile_data = {}
                    current_point_id = None

        # 查找测点数据表格（深度、声时、声速等）
        elif line.startswith('深度(m)') and '声时(us)' in line:
            print(f"  找到测点数据表格")
            # 跳过表头，开始读取数据
            i += 1
            point_counter = 1

            while i < len(lines):
                data_line = lines[i].strip()
                if not data_line or '波形' in data_line or re.match(r'\s*\d{3}-\d{2}\s+[\d\.]+', data_line):
                    break

                # 解析测点数据行
                parts = data_line.split()
                if len(parts) >= 5:
                    try:
                        depth = float(parts[0])
                        sound_time = float(parts[1])
                        amplitude = float(parts[3])
                        frequency = float(parts[4])

                        # 生成测点ID
                        point_id = f"{point_counter:03d}-01"

                        profile_data[point_id] = {
                            'distance_mm': depth * 1000,  # 转换为mm
                            'time_us': sound_time,
                            'intensity_db': amplitude,
                            'frequency_khz': frequency,
                            'waveform': []
                        }

                        point_counter += 1
                    except ValueError:
                        pass

                i += 1
            continue

        # 查找波形数据部分
        elif re.match(r'\s*\d{3}-\d{2}', line):
            # 这可能是波形数据头部行，有两种格式：
            # 格式1: 001-01	261.60	105.29	0.00  	8.80 (完整格式)
            # 格式2: 001-01  	15900.00 	105.98	142.63	0.00 (测点数据格式)
            # 格式3: 001-01  	15900 (不完整格式，只有深度)
            parts = line.split()
            if len(parts) >= 1:
                current_point_id = parts[0]
                print(f"    找到波形数据头部: {current_point_id}")

                # 如果这个测点不在profile_data中，创建它
                if current_point_id not in profile_data:
                    try:
                        if len(parts) >= 5:
                            # 完整格式
                            time_val = float(parts[1])
                            intensity_val = float(parts[2])
                            freq_val = float(parts[3])
                            distance_val = float(parts[4])

                            profile_data[current_point_id] = {
                                'distance_mm': distance_val * 1000,  # 转换为mm
                                'time_us': time_val,
                                'intensity_db': intensity_val,
                                'frequency_khz': freq_val,
                                'waveform': []
                            }
                        elif len(parts) >= 2:
                            # 不完整格式，使用默认值
                            distance_val = float(parts[1]) / 1000.0 if parts[1].replace('.', '').isdigit() else 0.0

                            profile_data[current_point_id] = {
                                'distance_mm': distance_val * 1000,  # 转换为mm
                                'time_us': 0.0,  # 默认值
                                'intensity_db': 0.0,  # 默认值
                                'frequency_khz': 0.0,  # 默认值
                                'waveform': []
                            }
                    except ValueError:
                        # 如果解析失败，创建一个默认的测点
                        profile_data[current_point_id] = {
                            'distance_mm': 0.0,
                            'time_us': 0.0,
                            'intensity_db': 0.0,
                            'frequency_khz': 0.0,
                            'waveform': []
                        }

        # 查找波形放大倍数等信息行（跳过）
        elif '波形放大倍数:' in line or '基线修正值:' in line or '波形首点延迟点数:' in line:
            pass  # 跳过这些设置信息行

        # 解析波形数据行（数字序列）
        elif current_point_id and re.match(r'^[\d\s]+$', line) and line.strip():
            # 确保这一行只包含数字和空格
            parts = line.split()
            if all(part.isdigit() for part in parts) and len(parts) > 5:  # 至少有5个数字才认为是波形数据
                waveform_values = [int(x) for x in parts]
                if current_point_id in profile_data:
                    profile_data[current_point_id]['waveform'].extend(waveform_values)
                    print(f"    为 {current_point_id} 添加了 {len(waveform_values)} 个波形点")  # 调试信息

        i += 1

    # 保存最后一个剖面的数据
    if current_profile and profile_data:
        components[current_profile] = profile_data.copy()
        print(f"保存剖面 {current_profile}，包含 {len(profile_data)} 个测点")

    # 清理和重命名剖面数据
    # 删除没有波形数据的剖面，重命名有波形数据的剖面
    cleaned_components = {}
    profile_mapping = {
        '剖面0': '1-2',
        '剖面1': '1-3',
        '剖面2': '2-3'
    }

    for name, data in components.items():
        # 检查是否有波形数据
        has_waveform = any(point_data.get('waveform', []) for point_data in data.values())

        if has_waveform:
            # 使用正确的剖面名称
            final_name = profile_mapping.get(name, name)
            cleaned_components[final_name] = data
            print(f"重命名剖面 {name} -> {final_name}，包含波形数据")
        else:
            print(f"跳过剖面 {name}，无波形数据")

    # 转换为列表格式
    result = []
    for name, data in cleaned_components.items():
        if data:  # 只包含有数据的构件
            result.append({
                'name': name,
                'data': data
            })

    return result

def create_excel_file_with_worksheets(components: List[Dict], output_path: str):
    """
    创建包含多个工作表的Excel文件，每个构件对应一个工作表
    """
    if not components:
        print("没有构件数据，跳过文件创建")
        return

    # 创建Excel写入器
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        for component in components:
            component_name = component['name']
            data = component['data']

            if not data:
                print(f"构件 {component_name} 没有数据，跳过")
                continue

            # 准备数据
            excel_data = []

            # 找到最大波形长度，用于确定列数
            max_waveform_length = 0
            for point_id, point_data in data.items():
                if len(point_data['waveform']) > max_waveform_length:
                    max_waveform_length = len(point_data['waveform'])

            # 计算所有测点的波形数据总和，用于计算平均值
            all_sums = []
            for point_id, point_data in data.items():
                if point_data['waveform']:
                    waveform_sum = sum(abs(x) for x in point_data['waveform'])
                    all_sums.append(waveform_sum)

            # 计算筛选后的平均值（类似excel_data_processor_minus_mid.py的逻辑）
            if len(all_sums) >= 9:
                # 找到最大的9个值
                sorted_sums = sorted(all_sums, reverse=True)
                top_9 = sorted_sums[:9]
                # 去掉3个最大值，取剩下6个的平均数
                remaining_6 = top_9[3:]
                average_of_6 = sum(remaining_6) / len(remaining_6) if remaining_6 else 0
            else:
                # 数据行数少于9，使用所有值的平均数
                average_of_6 = sum(all_sums) / len(all_sums) if all_sums else 0

            # 创建表头
            headers = ['测点序号', '距离(mm)', '数据总和', '筛选后数据平均', '比值']
            for i in range(max_waveform_length):
                headers.append(f'波形数据{i+1}')

            # 添加数据行
            for point_id in sorted(data.keys()):
                point_data = data[point_id]

                # 计算波形数据总和
                waveform_sum = sum(abs(x) for x in point_data['waveform']) if point_data['waveform'] else 0

                # 计算比值
                ratio = waveform_sum / average_of_6 if average_of_6 != 0 else 0

                row = [point_id, point_data['distance_mm'], waveform_sum, average_of_6, ratio]

                # 添加波形数据
                waveform = point_data['waveform']
                for i in range(max_waveform_length):
                    if i < len(waveform):
                        row.append(waveform[i])
                    else:
                        row.append('')  # 空值填充

                excel_data.append(row)

            # 创建DataFrame并写入工作表
            df = pd.DataFrame(excel_data, columns=headers)

            # 确保工作表名称符合Excel规范（最大31个字符，不能包含特殊字符）
            sheet_name = component_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace(':', '_').replace('[', '_').replace(']', '_')
            if len(sheet_name) > 31:
                sheet_name = sheet_name[:31]

            df.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"  工作表 '{sheet_name}': {len(data)} 个测点, 最大波形长度: {max_waveform_length}")

def create_waveform_csv_files(components: List[Dict], output_dir: str = '.'):
    """
    为每个构件创建包含波形数据的CSV文件（保留原有功能）
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for component in components:
        component_name = component['name']
        data = component['data']

        if not data:
            print(f"构件 {component_name} 没有数据，跳过")
            continue

        # 创建CSV文件名
        csv_filename = f"{component_name}_waveform.csv"
        csv_path = os.path.join(output_dir, csv_filename)

        # 准备CSV数据
        csv_data = []

        # 找到最大波形长度，用于确定列数
        max_waveform_length = 0
        for point_id, point_data in data.items():
            if len(point_data['waveform']) > max_waveform_length:
                max_waveform_length = len(point_data['waveform'])

        # 创建表头
        headers = ['point_id', 'distance_mm']
        for i in range(max_waveform_length):
            headers.append(f'waveform_{i+1}')

        # 添加数据行
        for point_id in sorted(data.keys()):
            point_data = data[point_id]
            row = [point_id, point_data['distance_mm']]

            # 添加波形数据
            waveform = point_data['waveform']
            for i in range(max_waveform_length):
                if i < len(waveform):
                    row.append(waveform[i])
                else:
                    row.append('')  # 空值填充

            csv_data.append(row)

        # 创建DataFrame并保存
        df = pd.DataFrame(csv_data, columns=headers)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')

        print(f"已创建文件: {csv_path}")
        print(f"  - 构件名称: {component_name}")
        print(f"  - 测点数量: {len(data)}")
        print(f"  - 最大波形长度: {max_waveform_length}")

        # 显示每个测点的波形长度
        for point_id in sorted(data.keys()):
            waveform_length = len(data[point_id]['waveform'])
            print(f"    {point_id}: {waveform_length} 个波形点")
        print()

def process_single_file(input_file: str, output_dir: str, output_format: str = 'excel'):
    """
    处理单个TXT文件

    Args:
        input_file: 输入的TXT文件路径
        output_dir: 输出目录
        output_format: 输出格式，'excel' 或 'csv'
    """
    print(f"\n处理文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return False

    try:
        # 读取文件
        print("正在读取文件...")
        content = read_file_with_encoding(input_file)

        # 提取构件数据
        print("正在提取构件和波形数据...")
        components = extract_waveform_data(content)

        if not components:
            print("未找到任何构件数据")
            return False

        print(f"找到 {len(components)} 个构件:")
        for comp in components:
            print(f"  - {comp['name']}: {len(comp['data'])} 个测点")

        # 生成输出文件名
        file_name = os.path.splitext(os.path.basename(input_file))[0]

        if output_format == 'excel':
            # 创建Excel文件（包含多个工作表）
            output_file = os.path.join(output_dir, f"{file_name}_waveform_processed.xlsx")
            print(f"\n正在创建Excel文件: {output_file}")
            create_excel_file_with_worksheets(components, output_file)
            print(f"已创建Excel文件: {output_file}")
        else:
            # 创建CSV文件（每个构件一个文件）
            print(f"\n正在创建CSV文件...")
            create_waveform_csv_files(components, output_dir)

        return True

    except Exception as e:
        print(f"处理文件 {input_file} 时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数 - 批量处理文件夹中的所有TXT文件
    """
    # 设置输入和输出目录为脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_dir = script_dir
    output_dir = script_dir  # 输出到脚本所在目录

    print("开始批量处理波形数据清洗...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 查找所有TXT文件（去重）
    txt_files = set()
    for ext in ['*.txt', '*.TXT']:
        txt_files.update(glob.glob(os.path.join(input_dir, ext)))
    txt_files = sorted(list(txt_files))

    if not txt_files:
        print(f"在目录 {input_dir} 中未找到任何TXT文件")
        return

    print(f"\n找到 {len(txt_files)} 个TXT文件:")
    for txt_file in txt_files:
        print(f"  - {os.path.basename(txt_file)}")

    # 默认使用Excel格式（包含多个工作表）
    output_format = 'excel'
    print(f"\n使用Excel格式 (.xlsx) - 每个TXT文件生成一个Excel文件，包含多个工作表")

    # 处理每个文件
    success_count = 0
    total_count = len(txt_files)

    for txt_file in txt_files:
        if process_single_file(txt_file, output_dir, output_format):
            success_count += 1

    print(f"\n批量处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件")

    if output_format == 'excel':
        print(f"Excel文件已保存到: {output_dir}")
        print("每个Excel文件包含多个工作表，每个工作表对应一个构件")
    else:
        print(f"CSV文件已保存到: {output_dir}")
        print("每个构件对应一个CSV文件")

if __name__ == "__main__":
    main()
