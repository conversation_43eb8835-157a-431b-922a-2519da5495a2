#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波形数据清洗脚本 - 批量处理文件夹中的所有TXT文件
自动处理同一文件夹下的所有txt数据文件，为每个txt文件生成一个Excel文件
Excel文件中包含多个工作表，每个工作表对应一个构件（如1-2，1-3，2-3等）
每个工作表包含测点序号、测距和波形数据
"""

import pandas as pd
import re
import csv
import os
import glob
from pathlib import Path
from typing import List, Dict, Set

def read_file_with_encoding(file_path: str) -> str:
    """
    尝试不同编码读取文件
    """
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"成功使用 {encoding} 编码读取文件")
            return content
        except UnicodeDecodeError:
            continue
    
    raise ValueError("无法使用任何编码读取文件")

def extract_waveform_data(content: str) -> List[Dict]:
    """
    提取包含波形数据的构件信息
    """
    components = {}
    
    # 查找原始数据集标记
    original_dataset_patterns = [
        r'/\*+.*ԭʼ.*���ݼ�.*\*+/',
        r'/\*+.*原始.*数据.*集.*\*+/',
        r'ԭʼ���ݼ�',
        r'原始数据集'
    ]

    original_dataset_match = None
    for pattern in original_dataset_patterns:
        original_dataset_match = re.search(pattern, content)
        if original_dataset_match:
            print(f"找到原始数据集标记")
            break

    if not original_dataset_match:
        print("未找到原始数据集标记")
        return []

    # 从原始数据集开始的内容
    dataset_content = content[original_dataset_match.end():]

    # 分割为段落，每个段落包含一个构件的完整信息
    component_patterns = [
        r'�������[:：]\s*([^\s\n]+)',
        r'构件名称[:：]\s*([^\s\n]+)'
    ]
    
    lines = dataset_content.split('\n')
    
    current_component = None
    component_data = {}
    in_table = False
    in_waveform = False
    in_waveform_data_section = False  # 新增：标识是否在实际的波形数据部分（而不是测点声参量表格）
    current_point_id = None
    
    for line in lines:
        line = line.strip()

        # 查找构件名称
        comp_match = None
        for pattern in component_patterns:
            comp_match = re.search(pattern, line)
            if comp_match:
                break
        if comp_match:
            # 保存之前的构件数据
            if current_component and component_data:
                components[current_component] = component_data.copy()
                component_data = {}
            
            current_component = comp_match.group(1)
            print(f"处理构件: {current_component}")
            in_table = False
            in_waveform = False
            continue
        
        # 查找测点声参量表格开始
        table_patterns = [
            '���в��������',
            '测点声参量',
            '****************'
        ]

        table_found = False
        for pattern in table_patterns:
            if pattern in line and current_component:
                print(f"  找到测点声参量表格")
                in_table = True
                in_waveform = False
                table_found = True
                break

        if table_found:
            continue

        # 查找波形数据开始
        waveform_patterns = [
            '�����������������',
            '波形',
            '各测点声参量及波形'
        ]

        waveform_found = False
        for pattern in waveform_patterns:
            if pattern in line and current_component:
                print(f"  找到波形数据开始")
                in_table = False
                in_waveform = True
                in_waveform_data_section = False  # 重置，还在测点声参量表格阶段
                waveform_found = True
                break

        if waveform_found:
            continue
        
        # 如果在测点声参量表格中，解析基本数据
        if in_table and current_component:
            # 跳过表头行
            if '������' in line or '���(mm)' in line or '��(us)' in line:
                continue

            # 解析测点数据行
            data_match = re.match(r'(\d{3}-\d{2})\s+(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.\d+)', line)
            if data_match:
                point_id = data_match.group(1)
                second_value = float(data_match.group(2))

                # 区分测点声参量表格和波形数据头部
                # 测点声参量表格：第二个值是距离（通常<100mm）
                # 波形数据头部：第二个值是时间（通常>100us）
                if second_value < 100 and not in_waveform_data_section:  # 这是测点声参量表格
                    distance = second_value
                    time_us = float(data_match.group(3))
                    intensity_db = float(data_match.group(4))
                    frequency_khz = float(data_match.group(5))

                    # 解析测点声参量
                    # print(f"  在测点声参量表格中解析{point_id}: 距离={distance}mm")  # 可选调试信息

                    if point_id not in component_data:
                        component_data[point_id] = {
                            'distance_mm': distance,
                            'time_us': time_us,
                            'intensity_db': intensity_db,
                            'frequency_khz': frequency_khz,
                            'waveform': []
                        }
                else:  # 这是波形数据头部，切换到波形数据处理模式
                    # print(f"  检测到波形数据头部（基于数值判断）: {point_id}, 第二个值={second_value}")  # 可选调试信息
                    in_waveform_data_section = True
                    # 手动处理这个波形数据头部行
                    current_point_id = point_id
                    # print(f"    找到波形数据头部: {current_point_id}")  # 可选调试信息
                    continue


        
        # 如果在波形数据中，解析波形
        if in_waveform and current_component:
            # 查找波形数据行的模式 (如: 001-01	261.60	105.29	0.00  	8.80)
            # 注意：波形数据头部的字段顺序是：测点ID, 时间, 强度, 频率, 距离
            waveform_header_match = re.match(r'\s*(\d{3}-\d{2})\s+[\d\.]+\s+[\d\.]+\s+[\d\.]+\s+[\d\.]+', line)
            if waveform_header_match:
                point_id = waveform_header_match.group(1)

                # 在波形数据部分，所有匹配测点ID格式的行都当作波形数据头部处理
                current_point_id = point_id
                # print(f"    找到波形数据头部: {current_point_id}")  # 可选调试信息
                in_waveform_data_section = True  # 确保进入波形数据处理模式
                continue

            # 跳过放大倍数、阈值等信息行
            if '���ηŴ���' in line or '��������ֵ' in line or '�����׵��ӳٵ���' in line:
                # print(f"    跳过设置信息行: {line[:20]}...")  # 可选调试信息
                continue

            # 解析波形数据行（数字序列）
            if current_point_id and re.match(r'^[\d\s]+$', line) and line.strip():
                waveform_values = [int(x) for x in line.split() if x.isdigit()]
                if waveform_values:
                    if current_point_id in component_data:
                        component_data[current_point_id]['waveform'].extend(waveform_values)
                        # print(f"    为 {current_point_id} 添加了 {len(waveform_values)} 个波形点")  # 可选调试信息
                    else:
                        print(f"    警告: 测点 {current_point_id} 不在component_data中")
    
    # 保存最后一个构件的数据
    if current_component and component_data:
        components[current_component] = component_data.copy()
    
    # 转换为列表格式
    result = []
    for name, data in components.items():
        if data:  # 只包含有数据的构件
            result.append({
                'name': name,
                'data': data
            })
    
    return result

def create_excel_file_with_worksheets(components: List[Dict], output_path: str):
    """
    创建包含多个工作表的Excel文件，每个构件对应一个工作表
    """
    if not components:
        print("没有构件数据，跳过文件创建")
        return

    # 创建Excel写入器
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        for component in components:
            component_name = component['name']
            data = component['data']

            if not data:
                print(f"构件 {component_name} 没有数据，跳过")
                continue

            # 准备数据
            excel_data = []

            # 找到最大波形长度，用于确定列数
            max_waveform_length = 0
            for point_id, point_data in data.items():
                if len(point_data['waveform']) > max_waveform_length:
                    max_waveform_length = len(point_data['waveform'])

            # 创建表头
            headers = ['测点序号', '距离(mm)']
            for i in range(max_waveform_length):
                headers.append(f'波形数据{i+1}')

            # 添加数据行
            for point_id in sorted(data.keys()):
                point_data = data[point_id]
                row = [point_id, point_data['distance_mm']]

                # 添加波形数据
                waveform = point_data['waveform']
                for i in range(max_waveform_length):
                    if i < len(waveform):
                        row.append(waveform[i])
                    else:
                        row.append('')  # 空值填充

                excel_data.append(row)

            # 创建DataFrame并写入工作表
            df = pd.DataFrame(excel_data, columns=headers)

            # 确保工作表名称符合Excel规范（最大31个字符，不能包含特殊字符）
            sheet_name = component_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace(':', '_').replace('[', '_').replace(']', '_')
            if len(sheet_name) > 31:
                sheet_name = sheet_name[:31]

            df.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"  工作表 '{sheet_name}': {len(data)} 个测点, 最大波形长度: {max_waveform_length}")

def create_waveform_csv_files(components: List[Dict], output_dir: str = '.'):
    """
    为每个构件创建包含波形数据的CSV文件（保留原有功能）
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for component in components:
        component_name = component['name']
        data = component['data']

        if not data:
            print(f"构件 {component_name} 没有数据，跳过")
            continue

        # 创建CSV文件名
        csv_filename = f"{component_name}_waveform.csv"
        csv_path = os.path.join(output_dir, csv_filename)

        # 准备CSV数据
        csv_data = []

        # 找到最大波形长度，用于确定列数
        max_waveform_length = 0
        for point_id, point_data in data.items():
            if len(point_data['waveform']) > max_waveform_length:
                max_waveform_length = len(point_data['waveform'])

        # 创建表头
        headers = ['point_id', 'distance_mm']
        for i in range(max_waveform_length):
            headers.append(f'waveform_{i+1}')

        # 添加数据行
        for point_id in sorted(data.keys()):
            point_data = data[point_id]
            row = [point_id, point_data['distance_mm']]

            # 添加波形数据
            waveform = point_data['waveform']
            for i in range(max_waveform_length):
                if i < len(waveform):
                    row.append(waveform[i])
                else:
                    row.append('')  # 空值填充

            csv_data.append(row)

        # 创建DataFrame并保存
        df = pd.DataFrame(csv_data, columns=headers)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')

        print(f"已创建文件: {csv_path}")
        print(f"  - 构件名称: {component_name}")
        print(f"  - 测点数量: {len(data)}")
        print(f"  - 最大波形长度: {max_waveform_length}")

        # 显示每个测点的波形长度
        for point_id in sorted(data.keys()):
            waveform_length = len(data[point_id]['waveform'])
            print(f"    {point_id}: {waveform_length} 个波形点")
        print()

def process_single_file(input_file: str, output_dir: str, output_format: str = 'excel'):
    """
    处理单个TXT文件

    Args:
        input_file: 输入的TXT文件路径
        output_dir: 输出目录
        output_format: 输出格式，'excel' 或 'csv'
    """
    print(f"\n处理文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return False

    try:
        # 读取文件
        print("正在读取文件...")
        content = read_file_with_encoding(input_file)

        # 提取构件数据
        print("正在提取构件和波形数据...")
        components = extract_waveform_data(content)

        if not components:
            print("未找到任何构件数据")
            return False

        print(f"找到 {len(components)} 个构件:")
        for comp in components:
            print(f"  - {comp['name']}: {len(comp['data'])} 个测点")

        # 生成输出文件名
        file_name = os.path.splitext(os.path.basename(input_file))[0]

        if output_format == 'excel':
            # 创建Excel文件（包含多个工作表）
            output_file = os.path.join(output_dir, f"{file_name}_waveform.xlsx")
            print(f"\n正在创建Excel文件: {output_file}")
            create_excel_file_with_worksheets(components, output_file)
            print(f"已创建Excel文件: {output_file}")
        else:
            # 创建CSV文件（每个构件一个文件）
            print(f"\n正在创建CSV文件...")
            create_waveform_csv_files(components, output_dir)

        return True

    except Exception as e:
        print(f"处理文件 {input_file} 时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数 - 批量处理文件夹中的所有TXT文件
    """
    # 设置输入和输出目录为脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_dir = script_dir
    output_dir = script_dir  # 输出到脚本所在目录

    print("开始批量处理波形数据清洗...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 查找所有TXT文件（去重）
    txt_files = set()
    for ext in ['*.txt', '*.TXT']:
        txt_files.update(glob.glob(os.path.join(input_dir, ext)))
    txt_files = sorted(list(txt_files))

    if not txt_files:
        print(f"在目录 {input_dir} 中未找到任何TXT文件")
        return

    print(f"\n找到 {len(txt_files)} 个TXT文件:")
    for txt_file in txt_files:
        print(f"  - {os.path.basename(txt_file)}")

    # 默认使用Excel格式（包含多个工作表）
    output_format = 'excel'
    print(f"\n使用Excel格式 (.xlsx) - 每个TXT文件生成一个Excel文件，包含多个工作表")

    # 处理每个文件
    success_count = 0
    total_count = len(txt_files)

    for txt_file in txt_files:
        if process_single_file(txt_file, output_dir, output_format):
            success_count += 1

    print(f"\n批量处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件")

    if output_format == 'excel':
        print(f"Excel文件已保存到: {output_dir}")
        print("每个Excel文件包含多个工作表，每个工作表对应一个构件")
    else:
        print(f"CSV文件已保存到: {output_dir}")
        print("每个构件对应一个CSV文件")

if __name__ == "__main__":
    main()
