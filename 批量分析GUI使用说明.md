# GZ传统分析GUI - 批量分析功能使用说明

## 概述
修改后的GZ分析GUI现在支持批量导入和分析所有符合"*_with_energy.txt"格式的文件，可以一次性处理多个桩基检测数据文件。

## 主要功能改进

### 1. 批量文件选择
- **文件夹选择**：选择包含多个 *_with_energy.txt 文件的文件夹
- **自动扫描**：自动识别文件夹中所有符合格式的文件
- **文件列表**：显示找到的所有文件，支持多选
- **选择控制**：提供"全选"和"全不选"按钮

### 2. 批量分析处理
- **并行处理**：逐个分析选中的文件
- **实时进度**：显示当前处理的文件和总体进度
- **错误处理**：单个文件失败不影响其他文件的分析
- **结果汇总**：自动统计所有文件的分析结果

### 3. 批量结果展示
- **汇总统计**：显示成功分析的文件数量和总截面数
- **桩类分布**：统计各种桩类的数量和百分比
- **详细报告**：生成包含所有文件分析结果的详细报告

### 4. 批量结果保存
- **多种格式**：支持TXT、JSON、CSV格式保存
- **详细信息**：包含每个文件的分析状态和结果
- **错误记录**：记录分析失败的文件和错误原因

## 使用步骤

### 步骤1：启动程序
```bash
python gz_analysis_gui.py
```

### 步骤2：选择文件夹
1. 点击"选择文件夹"按钮
2. 浏览并选择包含 *_with_energy.txt 文件的文件夹
3. 程序会自动扫描并显示找到的文件

### 步骤3：选择要分析的文件
1. 在文件列表中查看找到的所有文件
2. 使用鼠标选择要分析的文件（支持Ctrl+点击多选）
3. 或使用"全选"/"全不选"按钮快速选择
4. 点击"加载选中文件"按钮

### 步骤4：配置分析参数
1. 在"分析参数配置"区域设置分析选项：
   - 速度指标：是否启用速度分析
   - 幅度指标：是否启用幅度分析
   - 能量指标：是否启用能量分析
   - PSD指标：是否启用PSD分析
2. 设置深度范围限制（如需要）

### 步骤5：开始批量分析
1. 点击"开始批量分析"按钮
2. 观察进度条和当前处理文件信息
3. 等待所有文件分析完成

### 步骤6：查看结果
1. 在"批量分析结果"区域查看汇总信息：
   - 已分析文件数
   - 总截面数
   - 桩类分布统计
2. 在结果文本框中查看详细报告

### 步骤7：保存结果
1. 点击"保存批量结果"按钮
2. 选择保存格式（TXT/JSON/CSV）
3. 指定保存位置和文件名

## 界面说明

### 文件选择区域
- **文件夹路径输入框**：显示选中的文件夹路径
- **选择文件夹按钮**：浏览选择文件夹
- **扫描文件按钮**：重新扫描文件夹中的文件
- **文件列表框**：显示找到的所有 *_with_energy.txt 文件
- **全选/全不选按钮**：快速选择控制
- **加载选中文件按钮**：加载选中的文件进行分析

### 控制按钮区域
- **开始批量分析**：启动批量分析处理
- **保存批量结果**：保存分析结果到文件
- **清除结果**：清空所有结果和选择
- **加载/保存配置**：管理分析参数配置

### 进度显示区域
- **主进度条**：显示整体分析进度（0-100%）
- **状态信息**：显示当前操作状态
- **批量进度信息**：显示当前处理的文件（如：3/10 KBZ1-9_with_energy.txt）

### 结果显示区域
- **已分析文件数**：成功分析的文件数量
- **总截面数**：所有文件的截面总数
- **桩类分布**：各种桩类的统计信息
- **详细结果文本框**：完整的分析报告

## 输出格式说明

### TXT格式
- 人类可读的详细报告
- 包含汇总统计和每个文件的详细结果
- 适合查看和打印

### JSON格式
- 结构化数据格式
- 包含完整的分析结果和元数据
- 适合程序处理和数据交换

### CSV格式
- 表格格式，适合Excel打开
- 每行一个文件的分析结果
- 包含文件名、状态、桩类、截面数等信息

## 错误处理

### 常见问题
1. **未找到文件**：确保文件夹中包含 *_with_energy.txt 格式的文件
2. **分析失败**：检查文件格式是否正确，数据是否完整
3. **内存不足**：如果文件过多，可以分批处理

### 错误信息
- 程序会记录每个文件的处理状态
- 失败的文件会显示具体错误原因
- 错误信息会包含在保存的结果中

## 性能优化建议

1. **文件数量**：建议单次处理不超过50个文件
2. **文件大小**：确保每个文件大小合理（通常几十KB到几MB）
3. **系统资源**：分析过程中避免运行其他占用大量内存的程序

## 兼容性说明

- **向后兼容**：仍然支持单文件分析功能
- **文件格式**：支持标准的 *_with_energy.txt 格式
- **操作系统**：支持Windows、Linux、macOS

## 技术特性

- **多线程处理**：分析在后台线程运行，不阻塞界面
- **内存管理**：每个文件独立处理，避免内存累积
- **错误隔离**：单个文件错误不影响其他文件
- **进度反馈**：实时显示处理进度和状态

---

**版本信息**：批量分析功能 v2.0  
**更新日期**：2025年1月27日  
**兼容性**：支持所有标准 *_with_energy.txt 格式文件